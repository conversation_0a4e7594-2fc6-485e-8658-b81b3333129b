const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const WeightRecord = sequelize.define('WeightRecord', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    weight: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    age_in_days: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    recorded_by: {
      type: DataTypes.STRING,
      allowNull: true
    },
    puppy_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Puppy',
        key: 'id'
      }
    },
    owner_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'User',
        key: 'id'
      }
    }
  }, {
    indexes: [
      {
        fields: ['puppy_id']
      },
      {
        fields: ['owner_id']
      },
      {
        fields: ['date']
      },
      {
        fields: ['age_in_days']
      },
      {
        unique: true,
        fields: ['puppy_id', 'date']
      }
    ]
  });

  return WeightRecord;
};
