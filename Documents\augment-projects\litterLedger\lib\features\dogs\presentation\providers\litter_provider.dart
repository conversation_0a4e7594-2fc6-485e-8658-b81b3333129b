import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/litter.dart';
import '../../data/repositories/litter_repository.dart';
import '../../../../core/services/api_service.dart';

// Repository provider
final litterRepositoryProvider = Provider<LitterRepository>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  return LitterRepository(apiService);
});

// Litters list provider
final littersProvider = AsyncNotifierProvider<LittersNotifier, List<Litter>>(() {
  return LittersNotifier();
});

// Individual litter provider
final litterProvider = FutureProvider.family<Litter?, String>((ref, litterId) async {
  final repository = ref.read(litterRepositoryProvider);
  return await repository.getLitterById(litterId);
});

// Litter form provider for add/edit screens
final litterFormProvider = StateNotifierProvider<LitterFormNotifier, LitterFormState>((ref) {
  return LitterFormNotifier(ref.read(litterRepositoryProvider));
});

class LittersNotifier extends AsyncNotifier<List<Litter>> {
  @override
  Future<List<Litter>> build() async {
    // Temporarily disable litter fetching to isolate the issue
    print('🔍 LittersNotifier.build() called - returning empty list for now');
    return [];

    // final repository = ref.read(litterRepositoryProvider);
    // return await repository.getUserLitters();
  }

  Future<void> refresh() async {
    // Temporarily disable refresh to isolate the issue
    print('🔍 LittersNotifier.refresh() called - returning empty list for now');
    state = const AsyncValue.data([]);
    return;

    // state = const AsyncValue.loading();
    // state = await AsyncValue.guard(() async {
    //   final repository = ref.read(litterRepositoryProvider);
    //   return await repository.getUserLitters();
    // });
  }

  Future<void> addLitter(Litter litter) async {
    final repository = ref.read(litterRepositoryProvider);
    await repository.createLitter(litter);
    await refresh();
  }

  Future<void> updateLitter(Litter litter) async {
    final repository = ref.read(litterRepositoryProvider);
    await repository.updateLitter(litter);
    await refresh();
  }

  Future<void> deleteLitter(String litterId) async {
    final repository = ref.read(litterRepositoryProvider);
    await repository.deleteLitter(litterId);
    await refresh();
  }
}

class LitterFormState {
  final Litter? litter;
  final bool isLoading;
  final String? error;

  const LitterFormState({
    this.litter,
    this.isLoading = false,
    this.error,
  });

  LitterFormState copyWith({
    Litter? litter,
    bool? isLoading,
    String? error,
  }) {
    return LitterFormState(
      litter: litter ?? this.litter,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

class LitterFormNotifier extends StateNotifier<LitterFormState> {
  final LitterRepository _repository;

  LitterFormNotifier(this._repository) : super(const LitterFormState());

  void setLitter(Litter? litter) {
    state = state.copyWith(litter: litter);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  Future<bool> saveLitter(Litter litter) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      if (state.litter == null) {
        // Creating new litter
        await _repository.createLitter(litter);
      } else {
        // Updating existing litter
        await _repository.updateLitter(litter);
      }
      
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  Future<bool> deleteLitter(String litterId) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.deleteLitter(litterId);
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }
}
