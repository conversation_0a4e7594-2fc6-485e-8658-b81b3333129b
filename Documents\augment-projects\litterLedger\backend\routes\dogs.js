const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { Dog, User, HealthRecord } = require('../models');
const { authenticateToken, requirePremium, optionalAuth } = require('../middleware/auth');
const { Op } = require('sequelize');

const router = express.Router();

// Get all dogs for the authenticated user
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 20, search, breed, gender, status } = req.query;
    const offset = (page - 1) * limit;

    // Build where clause
    const where = { owner_id: req.user.userId };
    
    if (search) {
      where.name = { [Op.iLike]: `%${search}%` };
    }
    
    if (breed) {
      where.breed = { [Op.iLike]: `%${breed}%` };
    }
    
    if (gender) {
      where.gender = gender;
    }
    
    if (status) {
      where.health_status = status;
    }

    const dogs = await Dog.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']],
      include: [
        {
          model: User,
          as: 'owner',
          attributes: ['id', 'display_name', 'kennel_name']
        },
        {
          model: Dog,
          as: 'sire',
          attributes: ['id', 'name', 'breed']
        },
        {
          model: Dog,
          as: 'dam',
          attributes: ['id', 'name', 'breed']
        }
      ]
    });

    res.json({
      dogs: dogs.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: dogs.count,
        pages: Math.ceil(dogs.count / limit)
      }
    });

  } catch (error) {
    console.error('Get dogs error:', error);
    res.status(500).json({
      error: 'Failed to fetch dogs'
    });
  }
});

// Get public dogs (for browsing)
router.get('/public', optionalAuth, async (req, res) => {
  try {
    const { page = 1, limit = 20, search, breed, gender } = req.query;
    const offset = (page - 1) * limit;

    const where = { is_public: true };
    
    if (search) {
      where.name = { [Op.iLike]: `%${search}%` };
    }
    
    if (breed) {
      where.breed = { [Op.iLike]: `%${breed}%` };
    }
    
    if (gender) {
      where.gender = gender;
    }

    const dogs = await Dog.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']],
      include: [
        {
          model: User,
          as: 'owner',
          attributes: ['id', 'display_name', 'kennel_name', 'location']
        }
      ]
    });

    res.json({
      dogs: dogs.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: dogs.count,
        pages: Math.ceil(dogs.count / limit)
      }
    });

  } catch (error) {
    console.error('Get public dogs error:', error);
    res.status(500).json({
      error: 'Failed to fetch public dogs'
    });
  }
});

// Get single dog
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const where = { id };
    
    // If not authenticated or not the owner, only show public dogs
    if (!req.user) {
      where.is_public = true;
    }

    const dog = await Dog.findOne({
      where,
      include: [
        {
          model: User,
          as: 'owner',
          attributes: ['id', 'display_name', 'kennel_name', 'location', 'phone', 'email']
        },
        {
          model: Dog,
          as: 'sire',
          attributes: ['id', 'name', 'breed', 'registration_number']
        },
        {
          model: Dog,
          as: 'dam',
          attributes: ['id', 'name', 'breed', 'registration_number']
        },
        {
          model: Dog,
          as: 'siredOffspring',
          attributes: ['id', 'name', 'breed', 'birth_date']
        },
        {
          model: Dog,
          as: 'damOffspring',
          attributes: ['id', 'name', 'breed', 'birth_date']
        },
        {
          model: HealthRecord,
          as: 'healthRecords',
          where: req.user && req.user.userId === dog?.owner_id ? {} : { record_type: 'health_test' },
          required: false
        }
      ]
    });

    if (!dog) {
      return res.status(404).json({
        error: 'Dog not found'
      });
    }

    // Check if user owns this dog
    const isOwner = req.user && req.user.userId === dog.owner_id;
    
    // Hide sensitive information if not owner
    if (!isOwner) {
      dog.owner.phone = null;
      dog.owner.email = null;
      dog.notes = null;
    }

    res.json({ dog });

  } catch (error) {
    console.error('Get dog error:', error);
    res.status(500).json({
      error: 'Failed to fetch dog'
    });
  }
});

// Create new dog
router.post('/', [
  authenticateToken,
  body('name').trim().isLength({ min: 1 }),
  body('breed').trim().isLength({ min: 1 }),
  body('gender').isIn(['male', 'female'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    // Check if user has reached dog limit (free users limited to 3 dogs)
    if (!req.user.isPremium) {
      const dogCount = await Dog.count({
        where: { owner_id: req.user.userId }
      });
      
      if (dogCount >= 3) {
        return res.status(403).json({
          error: 'Dog limit reached',
          message: 'Free users can only have 3 dogs. Upgrade to premium for unlimited dogs.'
        });
      }
    }

    const dogData = {
      ...req.body,
      owner_id: req.user.userId
    };

    const dog = await Dog.create(dogData);

    // Fetch the created dog with associations
    const createdDog = await Dog.findByPk(dog.id, {
      include: [
        {
          model: User,
          as: 'owner',
          attributes: ['id', 'display_name', 'kennel_name']
        }
      ]
    });

    res.status(201).json({
      message: 'Dog created successfully',
      dog: createdDog
    });

  } catch (error) {
    console.error('Create dog error:', error);
    res.status(500).json({
      error: 'Failed to create dog'
    });
  }
});

// Update dog
router.put('/:id', [
  authenticateToken,
  body('name').optional().trim().isLength({ min: 1 }),
  body('breed').optional().trim().isLength({ min: 1 }),
  body('gender').optional().isIn(['male', 'female'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { id } = req.params;

    const dog = await Dog.findOne({
      where: {
        id,
        owner_id: req.user.userId
      }
    });

    if (!dog) {
      return res.status(404).json({
        error: 'Dog not found or access denied'
      });
    }

    await dog.update(req.body);

    // Fetch updated dog with associations
    const updatedDog = await Dog.findByPk(dog.id, {
      include: [
        {
          model: User,
          as: 'owner',
          attributes: ['id', 'display_name', 'kennel_name']
        }
      ]
    });

    res.json({
      message: 'Dog updated successfully',
      dog: updatedDog
    });

  } catch (error) {
    console.error('Update dog error:', error);
    res.status(500).json({
      error: 'Failed to update dog'
    });
  }
});

// Delete dog
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const dog = await Dog.findOne({
      where: {
        id,
        owner_id: req.user.userId
      }
    });

    if (!dog) {
      return res.status(404).json({
        error: 'Dog not found or access denied'
      });
    }

    await dog.destroy();

    res.json({
      message: 'Dog deleted successfully'
    });

  } catch (error) {
    console.error('Delete dog error:', error);
    res.status(500).json({
      error: 'Failed to delete dog'
    });
  }
});

// Get dog's health records
router.get('/:id/health', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Verify dog ownership
    const dog = await Dog.findOne({
      where: {
        id,
        owner_id: req.user.userId
      }
    });

    if (!dog) {
      return res.status(404).json({
        error: 'Dog not found or access denied'
      });
    }

    const healthRecords = await HealthRecord.findAll({
      where: { dog_id: id },
      order: [['date', 'DESC']]
    });

    res.json({ healthRecords });

  } catch (error) {
    console.error('Get health records error:', error);
    res.status(500).json({
      error: 'Failed to fetch health records'
    });
  }
});

module.exports = router;
