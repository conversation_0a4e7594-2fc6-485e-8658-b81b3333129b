import 'package:flutter/material.dart';
import '../../domain/models/puppy.dart';

class PuppyCard extends StatelessWidget {
  final Puppy puppy;
  final VoidCallback? onTap;

  const PuppyCard({
    super.key,
    required this.puppy,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              _buildGenderIcon(),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      puppy.displayName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        if (puppy.currentWeight != null) ...[
                          Icon(
                            Icons.monitor_weight,
                            size: 14,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${puppy.currentWeight!.toStringAsFixed(2)} kg',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Colors.grey[600],
                                ),
                          ),
                          const SizedBox(width: 12),
                        ],
                        if (puppy.collarColor != null) ...[
                          Icon(
                            Icons.circle,
                            size: 14,
                            color: _getCollarColor(puppy.collarColor!),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            puppy.collarColor!,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Colors.grey[600],
                                ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  _buildStatusBadge(puppy.status),
                  const SizedBox(height: 4),
                  _buildHealthBadge(puppy.healthStatus),
                ],
              ),
              const SizedBox(width: 8),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGenderIcon() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: puppy.gender == Gender.male 
            ? Colors.blue.withValues(alpha: 0.1)
            : Colors.pink.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        puppy.gender == Gender.male ? Icons.male : Icons.female,
        color: puppy.gender == Gender.male ? Colors.blue : Colors.pink,
        size: 20,
      ),
    );
  }

  Widget _buildStatusBadge(PuppyStatus status) {
    Color color;
    switch (status) {
      case PuppyStatus.alive:
        color = Colors.green;
        break;
      case PuppyStatus.deceased:
        color = Colors.red;
        break;
      case PuppyStatus.sold:
        color = Colors.blue;
        break;
      case PuppyStatus.kept:
        color = Colors.purple;
        break;
      case PuppyStatus.reserved:
        color = Colors.orange;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        status.name.toUpperCase(),
        style: TextStyle(
          fontSize: 9,
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
    );
  }

  Widget _buildHealthBadge(HealthStatus status) {
    if (status == HealthStatus.unknown) {
      return const SizedBox.shrink();
    }

    Color color;
    switch (status) {
      case HealthStatus.healthy:
        color = Colors.green;
        break;
      case HealthStatus.sick:
        color = Colors.red;
        break;
      case HealthStatus.injured:
        color = Colors.orange;
        break;
      case HealthStatus.recovering:
        color = Colors.blue;
        break;
      case HealthStatus.unknown:
        color = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        status.name.toUpperCase(),
        style: TextStyle(
          fontSize: 9,
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
    );
  }

  Color _getCollarColor(String colorName) {
    switch (colorName.toLowerCase()) {
      case 'red':
        return Colors.red;
      case 'blue':
        return Colors.blue;
      case 'green':
        return Colors.green;
      case 'yellow':
        return Colors.yellow;
      case 'orange':
        return Colors.orange;
      case 'purple':
        return Colors.purple;
      case 'pink':
        return Colors.pink;
      case 'black':
        return Colors.black;
      case 'white':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }
}
