const { DataTypes, Op } = require('sequelize');

module.exports = (sequelize) => {
  const Puppy = sequelize.define('Puppy', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    collar_color: {
      type: DataTypes.STRING,
      allowNull: true
    },
    gender: {
      type: DataTypes.ENUM('male', 'female'),
      allowNull: false
    },
    birth_weight: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true
    },
    current_weight: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true
    },
    color: {
      type: DataTypes.STRING,
      allowNull: true
    },
    markings: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('alive', 'deceased', 'sold', 'kept', 'reserved'),
      defaultValue: 'alive'
    },
    health_status: {
      type: DataTypes.ENUM('healthy', 'sick', 'injured', 'recovering', 'unknown'),
      defaultValue: 'unknown'
    },
    microchip_number: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true
    },
    registration_number: {
      type: DataTypes.STRING,
      allowNull: true
    },
    photos: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    sale_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true
    },
    sale_date: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    buyer_name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    buyer_contact: {
      type: DataTypes.STRING,
      allowNull: true
    },
    tags: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    litter_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Litter',
        key: 'id'
      }
    },
    owner_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'User',
        key: 'id'
      }
    }
  }, {
    indexes: [
      {
        fields: ['litter_id']
      },
      {
        fields: ['owner_id']
      },
      {
        fields: ['gender']
      },
      {
        fields: ['status']
      },
      {
        fields: ['health_status']
      },
      {
        unique: true,
        fields: ['microchip_number'],
        where: {
          microchip_number: {
            [Op.ne]: null
          }
        }
      }
    ]
  });

  return Puppy;
};
