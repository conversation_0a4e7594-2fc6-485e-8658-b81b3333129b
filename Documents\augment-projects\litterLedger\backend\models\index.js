const { Sequelize } = require('sequelize');
require('dotenv').config();

// Database configuration
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: process.env.DB_STORAGE || './database.sqlite',
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true
  }
});

// Import models
const User = require('./User')(sequelize);
const Dog = require('./Dog')(sequelize);
const Message = require('./Message')(sequelize);
const Chat = require('./Chat')(sequelize);
const HealthRecord = require('./HealthRecord')(sequelize);

// Define associations
User.hasMany(Dog, { foreignKey: 'owner_id', as: 'dogs' });
Dog.belongsTo(User, { foreignKey: 'owner_id', as: 'owner' });

User.hasMany(Message, { foreignKey: 'sender_id', as: 'sentMessages' });
User.hasMany(Message, { foreignKey: 'receiver_id', as: 'receivedMessages' });
Message.belongsTo(User, { foreignKey: 'sender_id', as: 'sender' });
Message.belongsTo(User, { foreignKey: 'receiver_id', as: 'receiver' });

Chat.belongsToMany(User, { through: 'ChatParticipants', as: 'participants' });
User.belongsToMany(Chat, { through: 'ChatParticipants', as: 'chats' });

Chat.hasMany(Message, { foreignKey: 'chat_id', as: 'messages' });
Message.belongsTo(Chat, { foreignKey: 'chat_id', as: 'chat' });

Dog.hasMany(HealthRecord, { foreignKey: 'dog_id', as: 'healthRecords' });
HealthRecord.belongsTo(Dog, { foreignKey: 'dog_id', as: 'dog' });

// Breeding relationships (self-referencing)
Dog.belongsTo(Dog, { foreignKey: 'sire_id', as: 'sire' });
Dog.belongsTo(Dog, { foreignKey: 'dam_id', as: 'dam' });
Dog.hasMany(Dog, { foreignKey: 'sire_id', as: 'siredOffspring' });
Dog.hasMany(Dog, { foreignKey: 'dam_id', as: 'damOffspring' });

module.exports = {
  sequelize,
  User,
  Dog,
  Message,
  Chat,
  HealthRecord
};
