const { Sequelize } = require('sequelize');
require('dotenv').config();

// Database configuration
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: process.env.DB_STORAGE || './database.sqlite',
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true
  }
});

// Import models
const User = require('./User')(sequelize);
const Dog = require('./Dog')(sequelize);
const Message = require('./Message')(sequelize);
const Chat = require('./Chat')(sequelize);
const HealthRecord = require('./HealthRecord')(sequelize);
const Litter = require('./Litter')(sequelize);
const Puppy = require('./Puppy')(sequelize);
const WeightRecord = require('./WeightRecord')(sequelize);

// Define associations
User.hasMany(Dog, { foreignKey: 'owner_id', as: 'dogs' });
Dog.belongsTo(User, { foreignKey: 'owner_id', as: 'owner' });

User.hasMany(Message, { foreignKey: 'sender_id', as: 'sentMessages' });
User.hasMany(Message, { foreignKey: 'receiver_id', as: 'receivedMessages' });
Message.belongsTo(User, { foreignKey: 'sender_id', as: 'sender' });
Message.belongsTo(User, { foreignKey: 'receiver_id', as: 'receiver' });

Chat.belongsToMany(User, { through: 'ChatParticipants', as: 'participants' });
User.belongsToMany(Chat, { through: 'ChatParticipants', as: 'chats' });

Chat.hasMany(Message, { foreignKey: 'chat_id', as: 'messages' });
Message.belongsTo(Chat, { foreignKey: 'chat_id', as: 'chat' });

Dog.hasMany(HealthRecord, { foreignKey: 'dog_id', as: 'healthRecords' });
HealthRecord.belongsTo(Dog, { foreignKey: 'dog_id', as: 'dog' });

// Breeding relationships (self-referencing)
Dog.belongsTo(Dog, { foreignKey: 'sire_id', as: 'sire' });
Dog.belongsTo(Dog, { foreignKey: 'dam_id', as: 'dam' });
Dog.hasMany(Dog, { foreignKey: 'sire_id', as: 'siredOffspring' });
Dog.hasMany(Dog, { foreignKey: 'dam_id', as: 'damOffspring' });

// Litter associations
User.hasMany(Litter, { foreignKey: 'owner_id', as: 'litters' });
Litter.belongsTo(User, { foreignKey: 'owner_id', as: 'owner' });

Dog.hasMany(Litter, { foreignKey: 'sire_id', as: 'siredLitters' });
Dog.hasMany(Litter, { foreignKey: 'dam_id', as: 'damLitters' });
Litter.belongsTo(Dog, { foreignKey: 'sire_id', as: 'sire' });
Litter.belongsTo(Dog, { foreignKey: 'dam_id', as: 'dam' });

// Puppy associations
Litter.hasMany(Puppy, { foreignKey: 'litter_id', as: 'puppies' });
Puppy.belongsTo(Litter, { foreignKey: 'litter_id', as: 'litter' });

User.hasMany(Puppy, { foreignKey: 'owner_id', as: 'puppies' });
Puppy.belongsTo(User, { foreignKey: 'owner_id', as: 'owner' });

// Weight record associations
Puppy.hasMany(WeightRecord, { foreignKey: 'puppy_id', as: 'weightRecords' });
WeightRecord.belongsTo(Puppy, { foreignKey: 'puppy_id', as: 'puppy' });

User.hasMany(WeightRecord, { foreignKey: 'owner_id', as: 'weightRecords' });
WeightRecord.belongsTo(User, { foreignKey: 'owner_id', as: 'owner' });

module.exports = {
  sequelize,
  User,
  Dog,
  Message,
  Chat,
  HealthRecord,
  Litter,
  Puppy,
  WeightRecord
};
