const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const HealthRecord = sequelize.define('HealthRecord', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    record_type: {
      type: DataTypes.ENUM(
        'vaccination',
        'vet_visit',
        'health_test',
        'genetic_test',
        'injury',
        'illness',
        'surgery',
        'medication',
        'other'
      ),
      allowNull: false
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    veterinarian: {
      type: DataTypes.STRING,
      allowNull: true
    },
    clinic: {
      type: DataTypes.STRING,
      allowNull: true
    },
    cost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true
    },
    results: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    next_due_date: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    attachments: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    dog_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Dog',
        key: 'id'
      }
    }
  }, {
    indexes: [
      {
        fields: ['dog_id']
      },
      {
        fields: ['record_type']
      },
      {
        fields: ['date']
      },
      {
        fields: ['next_due_date']
      }
    ]
  });

  // Instance methods
  HealthRecord.prototype.isOverdue = function() {
    if (!this.next_due_date) return false;
    return new Date() > new Date(this.next_due_date);
  };

  HealthRecord.prototype.isDueSoon = function(days = 30) {
    if (!this.next_due_date) return false;
    const dueDate = new Date(this.next_due_date);
    const checkDate = new Date();
    checkDate.setDate(checkDate.getDate() + days);
    return dueDate <= checkDate;
  };

  return HealthRecord;
};
