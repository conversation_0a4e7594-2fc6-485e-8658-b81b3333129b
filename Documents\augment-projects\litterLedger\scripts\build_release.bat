@echo off
REM LitterLedger Release Build Script for Windows
REM This script builds the app for Android release

setlocal enabledelayedexpansion

REM Configuration
set APP_NAME=LitterLedger
set VERSION=1.0.0
set BUILD_NUMBER=1

echo.
echo 🚀 Starting %APP_NAME% Release Build
echo Version: %VERSION% (%BUILD_NUMBER%)
echo.

REM Check if Flutter is installed
flutter --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Flutter is not installed or not in PATH
    exit /b 1
)

REM Check if we're in the right directory
if not exist "pubspec.yaml" (
    echo ❌ pubspec.yaml not found. Please run this script from the project root.
    exit /b 1
)

echo 📦 Cleaning Previous Builds
echo ----------------------------------------
flutter clean
if errorlevel 1 (
    echo ❌ Clean failed
    exit /b 1
)
echo ✅ Clean completed

echo.
echo 📦 Getting Dependencies
echo ----------------------------------------
flutter pub get
if errorlevel 1 (
    echo ❌ Failed to get dependencies
    exit /b 1
)
echo ✅ Dependencies updated

echo.
echo 📦 Running Code Analysis
echo ----------------------------------------
flutter analyze
if errorlevel 1 (
    echo ❌ Code analysis failed. Please fix issues before building.
    exit /b 1
)
echo ✅ Code analysis passed

echo.
echo 📦 Running Tests
echo ----------------------------------------
flutter test
if errorlevel 1 (
    echo ❌ Tests failed. Please fix failing tests before building.
    exit /b 1
)
echo ✅ All tests passed

echo.
echo 📦 Building Android Release
echo ----------------------------------------
echo Building Android App Bundle...
flutter build appbundle --release --build-name=%VERSION% --build-number=%BUILD_NUMBER%
if errorlevel 1 (
    echo ❌ Android App Bundle build failed
    exit /b 1
)
echo ✅ Android App Bundle built successfully
echo 📱 Location: build\app\outputs\bundle\release\app-release.aab

echo.
echo Building Android APK (for testing)...
flutter build apk --release --build-name=%VERSION% --build-number=%BUILD_NUMBER%
if errorlevel 1 (
    echo ❌ Android APK build failed
    exit /b 1
)
echo ✅ Android APK built successfully
echo 📱 Location: build\app\outputs\flutter-apk\app-release.apk

echo.
echo 📦 Generating Build Information
echo ----------------------------------------

REM Get current date and time
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "datestamp=%YYYY%-%MM%-%DD% %HH%:%Min%:%Sec%"

REM Get Flutter version
for /f "tokens=*" %%i in ('flutter --version ^| findstr "Flutter"') do set FLUTTER_VERSION=%%i

REM Get Dart version
for /f "tokens=*" %%i in ('dart --version') do set DART_VERSION=%%i

REM Create build info file
(
echo %APP_NAME% Build Information
echo ==========================
echo Version: %VERSION%
echo Build Number: %BUILD_NUMBER%
echo Build Date: %datestamp%
echo.
echo Android Outputs:
echo - App Bundle: build\app\outputs\bundle\release\app-release.aab
echo - APK: build\app\outputs\flutter-apk\app-release.apk
echo.
echo Build Environment:
echo - Flutter Version: %FLUTTER_VERSION%
echo - Dart Version: %DART_VERSION%
echo - OS: Windows
) > build_info.txt

echo ✅ Build information saved to build_info.txt

echo.
echo 📦 Build Artifacts Information
echo ----------------------------------------
if exist "build\app\outputs\bundle\release\app-release.aab" (
    for %%A in ("build\app\outputs\bundle\release\app-release.aab") do (
        set "size=%%~zA"
        set /a "sizeMB=!size! / 1048576"
        echo 📦 Android App Bundle: !sizeMB! MB
    )
)

if exist "build\app\outputs\flutter-apk\app-release.apk" (
    for %%A in ("build\app\outputs\flutter-apk\app-release.apk") do (
        set "size=%%~zA"
        set /a "sizeMB=!size! / 1048576"
        echo 📦 Android APK: !sizeMB! MB
    )
)

echo.
echo 📦 Build Summary
echo ----------------------------------------
echo ✅ Release build completed successfully!
echo.
echo Next Steps:
echo 1. Test the release builds on physical devices
echo 2. Upload Android App Bundle to Google Play Console
echo 3. Submit for review on Google Play Store
echo.
echo Important Notes:
echo - Test in-app purchases in sandbox environment
echo - Verify Firebase configuration for production
echo - Check app signing certificates
echo - Review app store metadata and screenshots
echo.
echo 🎉 Happy launching!

pause
