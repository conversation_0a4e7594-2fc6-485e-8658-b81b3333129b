enum LitterStatus { planned, pregnant, born, weaned, completed }

class Litter {
  final String id;
  final String name;
  final DateTime birthDate;
  final LitterStatus status;
  final String? notes;
  final String sireId;
  final String damId;
  final String ownerId;
  final int totalPuppies;
  final int alivePuppies;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Litter({
    required this.id,
    required this.name,
    required this.birthDate,
    required this.status,
    this.notes,
    required this.sireId,
    required this.damId,
    required this.ownerId,
    this.totalPuppies = 0,
    this.alivePuppies = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  // Calculate age in days
  int get ageInDays {
    if (status == LitterStatus.planned || status == LitterStatus.pregnant) {
      return 0;
    }
    return DateTime.now().difference(birthDate).inDays;
  }

  // Get formatted age string
  String get ageString {
    if (status == LitterStatus.planned) {
      return 'Planned';
    } else if (status == LitterStatus.pregnant) {
      final daysUntilBirth = birthDate.difference(DateTime.now()).inDays;
      if (daysUntilBirth > 0) {
        return '$daysUntilBirth days until birth';
      } else {
        return 'Due any day';
      }
    } else {
      final days = ageInDays;
      if (days == 0) {
        return 'Born today';
      } else if (days == 1) {
        return '1 day old';
      } else if (days < 7) {
        return '$days days old';
      } else if (days < 14) {
        return '1 week old';
      } else if (days < 21) {
        return '2 weeks old';
      } else if (days < 28) {
        return '3 weeks old';
      } else {
        final weeks = (days / 7).floor();
        return '$weeks weeks old';
      }
    }
  }

  // Get status color
  String get statusColor {
    switch (status) {
      case LitterStatus.planned:
        return '#FFA726'; // Orange
      case LitterStatus.pregnant:
        return '#AB47BC'; // Purple
      case LitterStatus.born:
        return '#66BB6A'; // Green
      case LitterStatus.weaned:
        return '#42A5F5'; // Blue
      case LitterStatus.completed:
        return '#78909C'; // Blue Grey
    }
  }

  // Convert to JSON for API
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'birth_date': birthDate.toIso8601String().split('T')[0],
      'status': status.name,
      'notes': notes,
      'sire_id': sireId,
      'dam_id': damId,
      'owner_id': ownerId,
      'total_puppies': totalPuppies,
      'alive_puppies': alivePuppies,
    };
  }

  // Create from JSON (API response)
  factory Litter.fromJson(Map<String, dynamic> json) {
    return Litter(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      birthDate: json['birth_date'] != null
          ? DateTime.parse(json['birth_date'].toString())
          : DateTime.now(),
      status: LitterStatus.values.firstWhere(
        (s) => s.name == json['status']?.toString(),
        orElse: () => LitterStatus.planned,
      ),
      notes: json['notes'] != null ? json['notes'].toString() : null,
      sireId: json['sire_id']?.toString() ?? '',
      damId: json['dam_id']?.toString() ?? '',
      ownerId: json['owner_id']?.toString() ?? '',
      totalPuppies: json['total_puppies'] is int ? json['total_puppies'] : int.tryParse(json['total_puppies']?.toString() ?? '0') ?? 0,
      alivePuppies: json['alive_puppies'] is int ? json['alive_puppies'] : int.tryParse(json['alive_puppies']?.toString() ?? '0') ?? 0,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'].toString())
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'].toString())
          : DateTime.now(),
    );
  }

  // Copy with method for updates
  Litter copyWith({
    String? name,
    DateTime? birthDate,
    LitterStatus? status,
    String? notes,
    String? sireId,
    String? damId,
    int? totalPuppies,
    int? alivePuppies,
  }) {
    return Litter(
      id: id,
      name: name ?? this.name,
      birthDate: birthDate ?? this.birthDate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      sireId: sireId ?? this.sireId,
      damId: damId ?? this.damId,
      ownerId: ownerId,
      totalPuppies: totalPuppies ?? this.totalPuppies,
      alivePuppies: alivePuppies ?? this.alivePuppies,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Litter && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Litter(id: $id, name: $name, status: $status)';
}
