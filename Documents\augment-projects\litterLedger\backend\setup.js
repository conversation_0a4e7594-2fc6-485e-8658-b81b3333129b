const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

console.log('🔧 Setting up LitterLedger Backend...\n');

// Generate secure JWT secret
const jwtSecret = crypto.randomBytes(64).toString('hex');
const jwtRefreshSecret = crypto.randomBytes(64).toString('hex');

// Create .env file
const envContent = `# LitterLedger Backend Configuration
# Generated on ${new Date().toISOString()}

# Server Configuration
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# Database Configuration (SQLite for development)
DB_STORAGE=./database.sqlite

# JWT Configuration (Auto-generated secure keys)
JWT_SECRET=${jwtSecret}
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=${jwtRefreshSecret}

# File Upload Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# Development
DEBUG=litterledger:*

# Google OAuth (Optional - configure if needed)
# GOOGLE_CLIENT_ID=your_google_client_id
# GOOGLE_CLIENT_SECRET=your_google_client_secret
# GOOGLE_CALLBACK_URL=http://localhost:3001/api/auth/google/callback

# Email Configuration (Optional - for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your_app_password
`;

try {
  // Write .env file
  fs.writeFileSync('.env', envContent);
  console.log('✅ Created .env file with secure configuration');

  // Create uploads directory
  const uploadsDir = './uploads';
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
    console.log('✅ Created uploads directory');
  }

  // Create logs directory
  const logsDir = './logs';
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
    console.log('✅ Created logs directory');
  }

  console.log('\n🎉 Setup completed successfully!');
  console.log('\nNext steps:');
  console.log('1. Review and customize .env file if needed');
  console.log('2. Run "npm run dev" to start the development server');
  console.log('3. Server will be available at http://localhost:3001');
  console.log('4. Health check: http://localhost:3001/health');
  console.log('\n📚 See README.md for detailed documentation');

} catch (error) {
  console.error('❌ Setup failed:', error.message);
  process.exit(1);
}
