import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class AuthService {
  static const String _baseUrl = 'http://localhost:3001/api';
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';
  
  // Secure storage for sensitive data
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
  );

  // Google Sign-In configuration
  static final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: ['email', 'profile'],
    // TODO: Replace with your actual Google OAuth client ID from Google Cloud Console
    // Get this from: https://console.cloud.google.com/apis/credentials
    clientId: kIsWeb ? 'YOUR_GOOGLE_CLIENT_ID.apps.googleusercontent.com' : null,
  );

  // Check if user is logged in
  static Future<bool> isLoggedIn() async {
    try {
      if (kIsWeb) {
        // Use SharedPreferences for web
        final prefs = await SharedPreferences.getInstance();
        final token = prefs.getString(_tokenKey);
        return token != null && token.isNotEmpty;
      } else {
        final token = await _secureStorage.read(key: _tokenKey);
        return token != null && token.isNotEmpty;
      }
    } catch (e) {
      print('Error checking login status: $e');
      return false;
    }
  }

  // Get stored user data
  static Future<Map<String, dynamic>?> getStoredUser() async {
    try {
      if (kIsWeb) {
        // Use SharedPreferences for web
        final prefs = await SharedPreferences.getInstance();
        final userJson = prefs.getString(_userKey);
        if (userJson != null) {
          return json.decode(userJson);
        }
      } else {
        final userJson = await _secureStorage.read(key: _userKey);
        if (userJson != null) {
          return json.decode(userJson);
        }
      }
    } catch (e) {
      print('Error getting stored user: $e');
    }
    return null;
  }

  // Store authentication data
  static Future<void> _storeAuthData(String token, Map<String, dynamic> user) async {
    try {
      if (kIsWeb) {
        // Use SharedPreferences for web
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_tokenKey, token);
        await prefs.setString(_userKey, json.encode(user));
      } else {
        await _secureStorage.write(key: _tokenKey, value: token);
        await _secureStorage.write(key: _userKey, value: json.encode(user));
      }
    } catch (e) {
      print('Error storing auth data: $e');
    }
  }

  // Clear authentication data
  static Future<void> clearAuthData() async {
    try {
      if (kIsWeb) {
        // Use SharedPreferences for web
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove(_tokenKey);
        await prefs.remove(_userKey);
      } else {
        await _secureStorage.delete(key: _tokenKey);
        await _secureStorage.delete(key: _userKey);
      }
    } catch (e) {
      print('Error clearing auth data: $e');
    }
  }

  // Email/Password Sign In
  static Future<Map<String, dynamic>> signInWithEmailPassword(
    String email, 
    String password
  ) async {
    try {
      print('🔄 Attempting email/password login for: $email');
      
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/login'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({
          'email': email,
          'password': password,
        }),
      ).timeout(const Duration(seconds: 10));

      print('📡 Response status: ${response.statusCode}');
      print('📄 Response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final token = data['token'];
        final user = data['user'];
        
        // Store authentication data
        await _storeAuthData(token, user);
        
        return {
          'success': true,
          'user': user,
          'token': token,
          'message': data['message'] ?? 'Login successful',
        };
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'Login failed');
      }
    } catch (e) {
      print('❌ Email/Password login error: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // Google Sign In
  static Future<Map<String, dynamic>> signInWithGoogle() async {
    try {
      print('🔄 Attempting Google Sign-In');

      if (kIsWeb) {
        // For web, use the Google Identity Services
        return await _signInWithGoogleWeb();
      } else {
        // For mobile platforms
        return await _signInWithGoogleMobile();
      }
    } catch (e) {
      print('❌ Google Sign-In error: $e');
      return {
        'success': false,
        'error': 'Google Sign-In failed: ${e.toString()}',
      };
    }
  }

  // Google Sign-In for Web
  static Future<Map<String, dynamic>> _signInWithGoogleWeb() async {
    try {
      // Try to use the Google Sign-In plugin first
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        return {
          'success': false,
          'error': 'Google Sign-In was cancelled',
        };
      }

      print('✅ Google Sign-In successful for: ${googleUser.email}');
      print('📝 Display Name: ${googleUser.displayName}');
      print('🖼️ Photo URL: ${googleUser.photoUrl}');

      // Get authentication details
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      return await _authenticateWithSocialProvider(
        provider: 'google',
        token: googleAuth.idToken ?? googleAuth.accessToken ?? 'google_${googleUser.id}',
        email: googleUser.email,
        displayName: googleUser.displayName ?? googleUser.email.split('@')[0],
        photoUrl: googleUser.photoUrl,
      );
    } catch (e) {
      print('❌ Google Web Sign-In error: $e');

      // If the plugin fails on web, we could implement a custom web flow here
      // For now, return an error
      return {
        'success': false,
        'error': 'Google Sign-In not configured for web. Please set up Google OAuth credentials.',
      };
    }
  }

  // Google Sign-In for Mobile
  static Future<Map<String, dynamic>> _signInWithGoogleMobile() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        return {
          'success': false,
          'error': 'Google Sign-In was cancelled',
        };
      }

      print('✅ Google Sign-In successful for: ${googleUser.email}');
      print('📝 Display Name: ${googleUser.displayName}');
      print('🖼️ Photo URL: ${googleUser.photoUrl}');

      // Get authentication details
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      return await _authenticateWithSocialProvider(
        provider: 'google',
        token: googleAuth.idToken ?? googleAuth.accessToken ?? 'google_${googleUser.id}',
        email: googleUser.email,
        displayName: googleUser.displayName ?? googleUser.email.split('@')[0],
        photoUrl: googleUser.photoUrl,
      );
    } catch (e) {
      print('❌ Google Mobile Sign-In error: $e');
      return {
        'success': false,
        'error': 'Google Sign-In failed: ${e.toString()}',
      };
    }
  }

  // Apple Sign In
  static Future<Map<String, dynamic>> signInWithApple() async {
    try {
      print('🔄 Attempting Apple Sign-In');

      if (kIsWeb) {
        // For web, use Apple's JS SDK
        return await _signInWithAppleWeb();
      } else {
        // For mobile platforms
        return await _signInWithAppleMobile();
      }
    } catch (e) {
      print('❌ Apple Sign-In error: $e');
      return {
        'success': false,
        'error': 'Apple Sign-In failed: ${e.toString()}',
      };
    }
  }

  // Apple Sign-In for Web
  static Future<Map<String, dynamic>> _signInWithAppleWeb() async {
    try {
      print('🌐 Apple Sign-In for web - requires Apple Developer setup');

      // For web, Apple Sign-In requires proper configuration in Apple Developer Console
      // This would typically use Apple's JS SDK
      return {
        'success': false,
        'error': 'Apple Sign-In for web requires Apple Developer configuration. Please set up Apple Sign-In in your Apple Developer account.',
      };
    } catch (e) {
      print('❌ Apple Web Sign-In error: $e');
      return {
        'success': false,
        'error': 'Apple Sign-In failed: ${e.toString()}',
      };
    }
  }

  // Apple Sign-In for Mobile
  static Future<Map<String, dynamic>> _signInWithAppleMobile() async {
    try {
      // Check if Apple Sign-In is available
      if (!await SignInWithApple.isAvailable()) {
        return {
          'success': false,
          'error': 'Apple Sign-In is not available on this device',
        };
      }

      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      print('✅ Apple Sign-In successful');
      print('📧 Email: ${credential.email}');
      print('👤 User ID: ${credential.userIdentifier}');
      print('📝 Given Name: ${credential.givenName}');
      print('📝 Family Name: ${credential.familyName}');

      // Create display name from Apple credential
      String displayName = 'Apple User';
      if (credential.givenName != null && credential.familyName != null) {
        displayName = '${credential.givenName} ${credential.familyName}';
      } else if (credential.email != null) {
        displayName = credential.email!.split('@')[0];
      }

      // Apple may not provide email on subsequent logins
      String email = credential.email ?? '${credential.userIdentifier}@privaterelay.appleid.com';

      return await _authenticateWithSocialProvider(
        provider: 'apple',
        token: credential.identityToken ?? credential.userIdentifier!,
        email: email,
        displayName: displayName,
      );
    } catch (e) {
      print('❌ Apple Mobile Sign-In error: $e');
      return {
        'success': false,
        'error': 'Apple Sign-In failed: ${e.toString()}',
      };
    }
  }

  // Public method for social authentication
  static Future<Map<String, dynamic>> signInWithSocialProvider({
    required String provider,
    required String token,
    required String email,
    required String displayName,
    String? photoUrl,
  }) async {
    return await _authenticateWithSocialProvider(
      provider: provider,
      token: token,
      email: email,
      displayName: displayName,
      photoUrl: photoUrl,
    );
  }

  // Authenticate with social provider (backend call)
  static Future<Map<String, dynamic>> _authenticateWithSocialProvider({
    required String provider,
    required String token,
    required String email,
    required String displayName,
    String? photoUrl,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/social'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({
          'provider': provider,
          'token': token,
          'email': email,
          'display_name': displayName,
          'photo_url': photoUrl,
        }),
      ).timeout(const Duration(seconds: 10));

      print('📡 Social auth response status: ${response.statusCode}');
      print('📄 Social auth response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final authToken = data['token'];
        final user = data['user'];
        
        // Store authentication data
        await _storeAuthData(authToken, user);
        
        return {
          'success': true,
          'user': user,
          'token': authToken,
          'message': data['message'] ?? 'Social login successful',
        };
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'Social authentication failed');
      }
    } catch (e) {
      print('❌ Social authentication error: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // Sign Out
  static Future<void> signOut() async {
    try {
      // Sign out from Google if signed in
      if (await _googleSignIn.isSignedIn()) {
        await _googleSignIn.signOut();
      }
      
      // Clear stored authentication data
      await clearAuthData();
      
      print('✅ Successfully signed out');
    } catch (e) {
      print('❌ Sign out error: $e');
    }
  }

  // Get stored auth token
  static Future<String?> getAuthToken() async {
    try {
      if (kIsWeb) {
        final prefs = await SharedPreferences.getInstance();
        return prefs.getString(_tokenKey);
      } else {
        return await _secureStorage.read(key: _tokenKey);
      }
    } catch (e) {
      print('Error getting auth token: $e');
      return null;
    }
  }
}
