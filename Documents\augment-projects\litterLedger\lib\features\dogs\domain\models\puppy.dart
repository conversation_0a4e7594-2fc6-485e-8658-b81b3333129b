enum Gender { male, female }

enum PuppyStatus { alive, deceased, sold, kept, reserved }

enum HealthStatus { healthy, sick, injured, recovering, unknown }

class Puppy {
  final String id;
  final String? name;
  final String? collarColor;
  final Gender gender;
  final double? birthWeight;
  final double? currentWeight;
  final String? color;
  final String? markings;
  final PuppyStatus status;
  final HealthStatus healthStatus;
  final String? microchipNumber;
  final String? registrationNumber;
  final List<String> photos;
  final String? notes;
  final double? salePrice;
  final DateTime? saleDate;
  final String? buyerName;
  final String? buyerContact;
  final List<String> tags;
  final String litterId;
  final String ownerId;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Puppy({
    required this.id,
    this.name,
    this.collarColor,
    required this.gender,
    this.birthWeight,
    this.currentWeight,
    this.color,
    this.markings,
    this.status = PuppyStatus.alive,
    this.healthStatus = HealthStatus.unknown,
    this.microchipNumber,
    this.registrationNumber,
    this.photos = const [],
    this.notes,
    this.salePrice,
    this.saleDate,
    this.buyerName,
    this.buyerContact,
    this.tags = const [],
    required this.litterId,
    required this.ownerId,
    required this.createdAt,
    required this.updatedAt,
  });

  // Get display name (name or collar color or gender)
  String get displayName {
    if (name != null && name!.isNotEmpty) {
      return name!;
    } else if (collarColor != null && collarColor!.isNotEmpty) {
      return '${collarColor!} collar';
    } else {
      return gender == Gender.male ? 'Male puppy' : 'Female puppy';
    }
  }

  // Convert to JSON for API
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'collar_color': collarColor,
      'gender': gender.name,
      'birth_weight': birthWeight,
      'current_weight': currentWeight,
      'color': color,
      'markings': markings,
      'status': status.name,
      'health_status': healthStatus.name,
      'microchip_number': microchipNumber,
      'registration_number': registrationNumber,
      'photos': photos,
      'notes': notes,
      'sale_price': salePrice,
      'sale_date': saleDate?.toIso8601String().split('T')[0],
      'buyer_name': buyerName,
      'buyer_contact': buyerContact,
      'tags': tags,
      'litter_id': litterId,
      'owner_id': ownerId,
    };
  }

  // Create from JSON (API response)
  factory Puppy.fromJson(Map<String, dynamic> json) {
    return Puppy(
      id: json['id'] ?? '',
      name: json['name'],
      collarColor: json['collar_color'],
      gender: Gender.values.firstWhere(
        (g) => g.name == json['gender'],
        orElse: () => Gender.male,
      ),
      birthWeight: json['birth_weight']?.toDouble(),
      currentWeight: json['current_weight']?.toDouble(),
      color: json['color'],
      markings: json['markings'],
      status: PuppyStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => PuppyStatus.alive,
      ),
      healthStatus: HealthStatus.values.firstWhere(
        (h) => h.name == json['health_status'],
        orElse: () => HealthStatus.unknown,
      ),
      microchipNumber: json['microchip_number'],
      registrationNumber: json['registration_number'],
      photos: List<String>.from(json['photos'] ?? []),
      notes: json['notes'],
      salePrice: json['sale_price']?.toDouble(),
      saleDate: json['sale_date'] != null
          ? DateTime.parse(json['sale_date'])
          : null,
      buyerName: json['buyer_name'],
      buyerContact: json['buyer_contact'],
      tags: List<String>.from(json['tags'] ?? []),
      litterId: json['litter_id'] ?? '',
      ownerId: json['owner_id'] ?? '',
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  // Copy with method for updates
  Puppy copyWith({
    String? name,
    String? collarColor,
    Gender? gender,
    double? birthWeight,
    double? currentWeight,
    String? color,
    String? markings,
    PuppyStatus? status,
    HealthStatus? healthStatus,
    String? microchipNumber,
    String? registrationNumber,
    List<String>? photos,
    String? notes,
    double? salePrice,
    DateTime? saleDate,
    String? buyerName,
    String? buyerContact,
    List<String>? tags,
  }) {
    return Puppy(
      id: id,
      name: name ?? this.name,
      collarColor: collarColor ?? this.collarColor,
      gender: gender ?? this.gender,
      birthWeight: birthWeight ?? this.birthWeight,
      currentWeight: currentWeight ?? this.currentWeight,
      color: color ?? this.color,
      markings: markings ?? this.markings,
      status: status ?? this.status,
      healthStatus: healthStatus ?? this.healthStatus,
      microchipNumber: microchipNumber ?? this.microchipNumber,
      registrationNumber: registrationNumber ?? this.registrationNumber,
      photos: photos ?? this.photos,
      notes: notes ?? this.notes,
      salePrice: salePrice ?? this.salePrice,
      saleDate: saleDate ?? this.saleDate,
      buyerName: buyerName ?? this.buyerName,
      buyerContact: buyerContact ?? this.buyerContact,
      tags: tags ?? this.tags,
      litterId: litterId,
      ownerId: ownerId,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Puppy && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Puppy(id: $id, name: $displayName, gender: $gender)';
}
