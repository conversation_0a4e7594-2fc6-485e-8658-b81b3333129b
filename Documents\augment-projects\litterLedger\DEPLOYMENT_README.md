# LitterLedger Deployment Guide

## Overview

LitterLedger is a professional dog breeding management application built with Flutter and Firebase. This guide covers the complete deployment process for both iOS App Store and Google Play Store.

## 🚀 Quick Start

### Prerequisites
- Flutter SDK (latest stable)
- Xcode (for iOS builds, macOS only)
- Android Studio or Android SDK
- Firebase project configured
- Apple Developer Account ($99/year)
- Google Play Developer Account ($25 one-time)

### Build Commands

**For Windows/Linux (Android only):**
```bash
cd scripts
./build_release.bat  # Windows
./build_release.sh   # Linux/macOS
```

**For macOS (iOS + Android):**
```bash
cd scripts
chmod +x build_release.sh
./build_release.sh
```

## 📱 Platform-Specific Deployment

### iOS App Store

#### 1. Xcode Configuration
```bash
# Open iOS project in Xcode
open ios/Runner.xcworkspace
```

#### 2. App Store Connect Setup
- Create app in App Store Connect
- Configure app information
- Upload screenshots and metadata
- Set pricing (Free with IAP)

#### 3. Build and Upload
1. Select "Any iOS Device" in Xcode
2. Product → Archive
3. Upload to App Store Connect
4. Submit for review

#### 4. Required Assets
- App Icon: 1024x1024px
- Screenshots: Multiple device sizes
- Privacy Policy URL
- App description and keywords

### Google Play Store

#### 1. Play Console Setup
- Create app in Google Play Console
- Complete store listing
- Upload app bundle
- Configure pricing and distribution

#### 2. Build and Upload
```bash
# Build app bundle
flutter build appbundle --release

# Upload to Play Console
# File: build/app/outputs/bundle/release/app-release.aab
```

#### 3. Required Assets
- App Icon: 512x512px
- Feature Graphic: 1024x500px
- Screenshots: Phone and tablet
- Privacy Policy URL

## 🔧 Configuration Files

### Firebase Setup
1. **Production Project**: Create separate Firebase project for production
2. **Configuration Files**:
   - `android/app/google-services.json`
   - `ios/Runner/GoogleService-Info.plist`
3. **Security Rules**: Deploy production Firestore rules
4. **Environment**: Switch from debug to production

### App Signing

#### Android
```bash
# Generate keystore (one-time)
keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload

# Configure in android/key.properties
storePassword=<password>
keyPassword=<password>
keyAlias=upload
storeFile=<path-to-keystore>
```

#### iOS
- Use Xcode automatic signing
- Or configure manual signing with distribution certificate

## 📋 Pre-Deployment Checklist

### Code Quality
- [ ] All features implemented and tested
- [ ] No debug code or console logs
- [ ] Error handling implemented
- [ ] Performance optimized
- [ ] Memory leaks fixed

### Security
- [ ] API keys secured
- [ ] Firebase security rules configured
- [ ] User data encrypted
- [ ] Privacy policy accessible
- [ ] Terms of service accessible

### App Store Requirements
- [ ] App icons created (all sizes)
- [ ] Screenshots captured
- [ ] App description written
- [ ] Keywords researched
- [ ] Age rating completed
- [ ] In-app purchases configured

### Testing
- [ ] Device testing completed
- [ ] In-app purchase testing
- [ ] Offline functionality tested
- [ ] Dark mode tested
- [ ] Accessibility tested

## 💰 Monetization Setup

### In-App Purchases
1. **App Store Connect**: Create in-app purchase products
2. **Google Play Console**: Create in-app products
3. **Product IDs**: Ensure consistency across platforms
4. **Testing**: Use sandbox environments

### Product Configuration
- **Product ID**: `full_access`
- **Type**: Non-consumable
- **Price**: $2.99 USD
- **Title**: "LitterLedger Premium"
- **Description**: "Unlock all premium features"

## 🔍 Testing Strategy

### Pre-Release Testing
1. **Internal Testing**: Development team
2. **Closed Testing**: Beta users (TestFlight/Internal Testing)
3. **Open Testing**: Public beta (optional)

### Test Scenarios
- [ ] User registration and login
- [ ] Dog profile creation and editing
- [ ] Photo upload and storage
- [ ] In-app messaging
- [ ] Premium purchase flow
- [ ] Data sync and offline mode
- [ ] Theme switching

## 📊 Analytics and Monitoring

### Firebase Analytics
- User engagement tracking
- Feature usage analytics
- Crash reporting (Crashlytics)
- Performance monitoring

### Key Metrics to Track
- Daily/Monthly Active Users
- Premium conversion rate
- Feature adoption rates
- Crash-free sessions
- App store ratings

## 🚨 Post-Launch Monitoring

### Week 1
- [ ] Monitor crash reports
- [ ] Check user reviews
- [ ] Verify in-app purchases
- [ ] Monitor server performance
- [ ] Track download numbers

### Month 1
- [ ] Analyze user behavior
- [ ] Gather feature feedback
- [ ] Plan first update
- [ ] Monitor competition
- [ ] Optimize app store listing

## 🔄 Update Process

### Version Management
```bash
# Update version in pubspec.yaml
version: 1.0.1+2

# Build new release
flutter build appbundle --release --build-name=1.0.1 --build-number=2
```

### Release Notes Template
```
What's New in v1.0.1:
• Bug fixes and performance improvements
• Enhanced user interface
• New features based on user feedback

Thank you for using LitterLedger!
```

## 📞 Support and Maintenance

### Customer Support
- **Email**: <EMAIL>
- **Response Time**: 24-48 hours
- **FAQ**: In-app help section
- **Bug Reports**: Integrated feedback system

### Maintenance Schedule
- **Security Updates**: As needed
- **Feature Updates**: Monthly
- **Major Releases**: Quarterly
- **Bug Fixes**: Weekly if needed

## 📚 Resources

### Documentation
- [Flutter Deployment Guide](https://flutter.dev/docs/deployment)
- [App Store Review Guidelines](https://developer.apple.com/app-store/review/guidelines/)
- [Google Play Policy](https://play.google.com/about/developer-content-policy/)
- [Firebase Documentation](https://firebase.google.com/docs)

### Tools
- [App Store Connect](https://appstoreconnect.apple.com)
- [Google Play Console](https://play.google.com/console)
- [Firebase Console](https://console.firebase.google.com)
- [Flutter DevTools](https://flutter.dev/docs/development/tools/devtools)

## 🆘 Troubleshooting

### Common Issues
1. **Build Failures**: Check Flutter and dependency versions
2. **Signing Issues**: Verify certificates and provisioning profiles
3. **Firebase Errors**: Check configuration files and project settings
4. **Store Rejections**: Review guidelines and fix reported issues

### Getting Help
- Flutter Community: [Discord](https://discord.gg/flutter)
- Stack Overflow: Tag questions with `flutter`
- GitHub Issues: Report bugs to respective repositories

---

**Last Updated**: December 2024
**Version**: 1.0.0
**Maintainer**: LitterLedger Development Team
