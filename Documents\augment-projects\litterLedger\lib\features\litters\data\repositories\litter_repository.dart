import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../domain/models/litter.dart';
import '../../domain/models/puppy.dart';
import '../../domain/models/weight_record.dart';
import '../../../../core/services/auth_service.dart';

class LitterRepository {
  static const String baseUrl = 'http://localhost:3001/api';

  // Get auth headers
  Future<Map<String, String>> _getHeaders() async {
    final token = await AuthService.getAuthToken();
    print('🔑 Auth token: ${token != null ? 'Present (${token.substring(0, 10)}...)' : 'Missing'}');
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // LITTER OPERATIONS

  // Get all litters for the current user
  Future<List<Litter>> getUserLitters() async {
    try {
      print('📡 Fetching litters from: $baseUrl/litters');
      final headers = await _getHeaders();
      print('📋 Request headers: $headers');

      final response = await http.get(
        Uri.parse('$baseUrl/litters'),
        headers: headers,
      );

      print('📡 Response status: ${response.statusCode}');
      print('📄 Response body: ${response.body}');

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        print('✅ Successfully loaded ${data.length} litters');

        final litters = <Litter>[];
        for (int i = 0; i < data.length; i++) {
          final litter = Litter.fromJson(data[i]);
          litters.add(litter);
        }

        print('✅ Successfully parsed all ${litters.length} litters');
        return litters;
      } else {
        print('❌ Failed to load litters: ${response.statusCode}');
        throw Exception('Failed to load litters: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Error fetching litters: $e');
      throw Exception('Error fetching litters: $e');
    }
  }

  // Get a specific litter by ID
  Future<Litter?> getLitterById(String litterId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/litters/$litterId'),
        headers: await _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Litter.fromJson(data);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('Failed to load litter: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching litter: $e');
    }
  }

  // Create a new litter
  Future<Litter> createLitter(Litter litter) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/litters'),
        headers: await _getHeaders(),
        body: json.encode(litter.toJson()),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return Litter.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'Failed to create litter');
      }
    } catch (e) {
      throw Exception('Error creating litter: $e');
    }
  }

  // Update an existing litter
  Future<Litter> updateLitter(Litter litter) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/litters/${litter.id}'),
        headers: await _getHeaders(),
        body: json.encode(litter.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Litter.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'Failed to update litter');
      }
    } catch (e) {
      throw Exception('Error updating litter: $e');
    }
  }

  // Delete a litter
  Future<void> deleteLitter(String litterId) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/litters/$litterId'),
        headers: await _getHeaders(),
      );

      if (response.statusCode != 200 && response.statusCode != 204) {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'Failed to delete litter');
      }
    } catch (e) {
      throw Exception('Error deleting litter: $e');
    }
  }

  // PUPPY OPERATIONS

  // Get all puppies for a specific litter
  Future<List<Puppy>> getLitterPuppies(String litterId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/litters/$litterId/puppies'),
        headers: await _getHeaders(),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => Puppy.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load puppies: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching puppies: $e');
    }
  }

  // Get all puppies for the current user
  Future<List<Puppy>> getUserPuppies() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/puppies'),
        headers: await _getHeaders(),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => Puppy.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load puppies: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching puppies: $e');
    }
  }

  // Create a new puppy
  Future<Puppy> createPuppy(Puppy puppy) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/puppies'),
        headers: await _getHeaders(),
        body: json.encode(puppy.toJson()),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return Puppy.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'Failed to create puppy');
      }
    } catch (e) {
      throw Exception('Error creating puppy: $e');
    }
  }

  // Update an existing puppy
  Future<Puppy> updatePuppy(Puppy puppy) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/puppies/${puppy.id}'),
        headers: await _getHeaders(),
        body: json.encode(puppy.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Puppy.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'Failed to update puppy');
      }
    } catch (e) {
      throw Exception('Error updating puppy: $e');
    }
  }

  // Delete a puppy
  Future<void> deletePuppy(String puppyId) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/puppies/$puppyId'),
        headers: await _getHeaders(),
      );

      if (response.statusCode != 200 && response.statusCode != 204) {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'Failed to delete puppy');
      }
    } catch (e) {
      throw Exception('Error deleting puppy: $e');
    }
  }

  // WEIGHT RECORD OPERATIONS

  // Get weight records for a specific puppy
  Future<List<WeightRecord>> getPuppyWeightRecords(String puppyId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/puppies/$puppyId/weights'),
        headers: await _getHeaders(),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => WeightRecord.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load weight records: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching weight records: $e');
    }
  }

  // Get all weight records for the current user
  Future<List<WeightRecord>> getUserWeightRecords() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/weight-records'),
        headers: await _getHeaders(),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => WeightRecord.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load weight records: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching weight records: $e');
    }
  }

  // Create a new weight record
  Future<WeightRecord> createWeightRecord(WeightRecord record) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/weight-records'),
        headers: await _getHeaders(),
        body: json.encode(record.toJson()),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return WeightRecord.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'Failed to create weight record');
      }
    } catch (e) {
      throw Exception('Error creating weight record: $e');
    }
  }

  // Update an existing weight record
  Future<WeightRecord> updateWeightRecord(WeightRecord record) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/weight-records/${record.id}'),
        headers: await _getHeaders(),
        body: json.encode(record.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return WeightRecord.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'Failed to update weight record');
      }
    } catch (e) {
      throw Exception('Error updating weight record: $e');
    }
  }

  // Delete a weight record
  Future<void> deleteWeightRecord(String recordId) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/weight-records/$recordId'),
        headers: await _getHeaders(),
      );

      if (response.statusCode != 200 && response.statusCode != 204) {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'Failed to delete weight record');
      }
    } catch (e) {
      throw Exception('Error deleting weight record: $e');
    }
  }
}
