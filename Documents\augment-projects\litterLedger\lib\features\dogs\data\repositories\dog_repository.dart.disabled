import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:io';
import '../../domain/models/dog.dart';

class DogRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Get current user ID
  String? get _currentUserId => _auth.currentUser?.uid;

  // Collection reference
  CollectionReference get _dogsCollection => _firestore.collection('dogs');

  // Get all dogs for current user
  Stream<List<Dog>> getUserDogs() {
    if (_currentUserId == null) {
      return Stream.value([]);
    }

    return _dogsCollection
        .where('ownerId', isEqualTo: _currentUserId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => Dog.fromFirestore(doc)).toList());
  }

  // Get a specific dog by ID
  Future<Dog?> getDogById(String dogId) async {
    try {
      final doc = await _dogsCollection.doc(dogId).get();
      if (doc.exists) {
        return Dog.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get dog: $e');
    }
  }

  // Add a new dog
  Future<String> addDog(Dog dog) async {
    if (_currentUserId == null) {
      throw Exception('User not authenticated');
    }

    try {
      final docRef = await _dogsCollection.add(dog.toFirestore());
      return docRef.id;
    } catch (e) {
      throw Exception('Failed to add dog: $e');
    }
  }

  // Update an existing dog
  Future<void> updateDog(Dog dog) async {
    if (_currentUserId == null) {
      throw Exception('User not authenticated');
    }

    try {
      await _dogsCollection.doc(dog.id).update(dog.toFirestore());
    } catch (e) {
      throw Exception('Failed to update dog: $e');
    }
  }

  // Delete a dog
  Future<void> deleteDog(String dogId) async {
    if (_currentUserId == null) {
      throw Exception('User not authenticated');
    }

    try {
      // Get the dog first to check ownership and get photo URL
      final dog = await getDogById(dogId);
      if (dog == null) {
        throw Exception('Dog not found');
      }

      if (dog.ownerId != _currentUserId) {
        throw Exception('Not authorized to delete this dog');
      }

      // Delete photo from storage if it exists
      if (dog.photoUrl != null && dog.photoUrl!.isNotEmpty) {
        try {
          await _storage.refFromURL(dog.photoUrl!).delete();
        } catch (e) {
          // Photo deletion failed, but continue with dog deletion
          // Log error in production app
        }
      }

      // Delete the dog document
      await _dogsCollection.doc(dogId).delete();
    } catch (e) {
      throw Exception('Failed to delete dog: $e');
    }
  }

  // Upload dog photo
  Future<String> uploadDogPhoto(String dogId, File imageFile) async {
    if (_currentUserId == null) {
      throw Exception('User not authenticated');
    }

    try {
      final fileName = 'dogs/$_currentUserId/$dogId/${DateTime.now().millisecondsSinceEpoch}.jpg';
      final ref = _storage.ref().child(fileName);
      
      final uploadTask = ref.putFile(imageFile);
      final snapshot = await uploadTask;
      
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw Exception('Failed to upload photo: $e');
    }
  }

  // Delete dog photo
  Future<void> deleteDogPhoto(String photoUrl) async {
    try {
      await _storage.refFromURL(photoUrl).delete();
    } catch (e) {
      throw Exception('Failed to delete photo: $e');
    }
  }

  // Search dogs by name or breed
  Future<List<Dog>> searchDogs(String query) async {
    if (_currentUserId == null) {
      return [];
    }

    try {
      final nameQuery = await _dogsCollection
          .where('ownerId', isEqualTo: _currentUserId)
          .where('name', isGreaterThanOrEqualTo: query)
          .where('name', isLessThanOrEqualTo: '$query\uf8ff')
          .get();

      final breedQuery = await _dogsCollection
          .where('ownerId', isEqualTo: _currentUserId)
          .where('breed', isGreaterThanOrEqualTo: query)
          .where('breed', isLessThanOrEqualTo: '$query\uf8ff')
          .get();

      final Set<String> seenIds = {};
      final List<Dog> results = [];

      // Add results from name query
      for (final doc in nameQuery.docs) {
        if (!seenIds.contains(doc.id)) {
          results.add(Dog.fromFirestore(doc));
          seenIds.add(doc.id);
        }
      }

      // Add results from breed query
      for (final doc in breedQuery.docs) {
        if (!seenIds.contains(doc.id)) {
          results.add(Dog.fromFirestore(doc));
          seenIds.add(doc.id);
        }
      }

      return results;
    } catch (e) {
      throw Exception('Failed to search dogs: $e');
    }
  }

  // Get dogs by breed
  Future<List<Dog>> getDogsByBreed(String breed) async {
    if (_currentUserId == null) {
      return [];
    }

    try {
      final snapshot = await _dogsCollection
          .where('ownerId', isEqualTo: _currentUserId)
          .where('breed', isEqualTo: breed)
          .orderBy('name')
          .get();

      return snapshot.docs.map((doc) => Dog.fromFirestore(doc)).toList();
    } catch (e) {
      throw Exception('Failed to get dogs by breed: $e');
    }
  }

  // Get breeding eligible dogs
  Future<List<Dog>> getBreedingEligibleDogs() async {
    if (_currentUserId == null) {
      return [];
    }

    try {
      final snapshot = await _dogsCollection
          .where('ownerId', isEqualTo: _currentUserId)
          .where('isBreedingEligible', isEqualTo: true)
          .orderBy('name')
          .get();

      return snapshot.docs.map((doc) => Dog.fromFirestore(doc)).toList();
    } catch (e) {
      throw Exception('Failed to get breeding eligible dogs: $e');
    }
  }

  // Get dog statistics
  Future<Map<String, dynamic>> getDogStatistics() async {
    if (_currentUserId == null) {
      return {};
    }

    try {
      final snapshot = await _dogsCollection
          .where('ownerId', isEqualTo: _currentUserId)
          .get();

      final dogs = snapshot.docs.map((doc) => Dog.fromFirestore(doc)).toList();

      final Map<String, int> breedCounts = {};
      int maleCount = 0;
      int femaleCount = 0;
      int breedingEligibleCount = 0;

      for (final dog in dogs) {
        // Count by breed
        breedCounts[dog.breed] = (breedCounts[dog.breed] ?? 0) + 1;

        // Count by gender
        if (dog.gender == Gender.male) {
          maleCount++;
        } else {
          femaleCount++;
        }

        // Count breeding eligible
        if (dog.isBreedingEligible) {
          breedingEligibleCount++;
        }
      }

      return {
        'totalDogs': dogs.length,
        'maleCount': maleCount,
        'femaleCount': femaleCount,
        'breedingEligibleCount': breedingEligibleCount,
        'breedCounts': breedCounts,
      };
    } catch (e) {
      throw Exception('Failed to get dog statistics: $e');
    }
  }
}
