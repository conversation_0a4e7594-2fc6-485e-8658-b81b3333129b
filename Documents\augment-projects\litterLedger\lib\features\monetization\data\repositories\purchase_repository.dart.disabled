import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:in_app_purchase/in_app_purchase.dart' as iap;
import 'dart:async';
import 'dart:io';
import '../../domain/models/purchase.dart';

class PurchaseRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final iap.InAppPurchase _inAppPurchase = iap.InAppPurchase.instance;

  // Get current user ID
  String? get _currentUserId => _auth.currentUser?.uid;

  // Collection references
  CollectionReference get _purchasesCollection => _firestore.collection('purchases');
  CollectionReference get _subscriptionsCollection => _firestore.collection('subscriptions');

  // Stream controller for purchase updates
  late StreamSubscription<List<iap.PurchaseDetails>> _purchaseSubscription;

  // Initialize in-app purchases
  Future<void> initialize() async {
    final bool available = await _inAppPurchase.isAvailable();
    if (!available) {
      throw Exception('In-app purchases not available');
    }

    // Listen to purchase updates
    _purchaseSubscription = _inAppPurchase.purchaseStream.listen(
      _handlePurchaseUpdates,
      onError: (error) {
        // Handle purchase stream errors
      },
    );
  }

  // Dispose resources
  void dispose() {
    _purchaseSubscription.cancel();
  }

  // Get available products
  Future<List<iap.ProductDetails>> getAvailableProducts() async {
    try {
      const Set<String> productIds = {'full_access'};
      final iap.ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(productIds);
      
      if (response.error != null) {
        throw Exception('Failed to get products: ${response.error!.message}');
      }

      return response.productDetails;
    } catch (e) {
      throw Exception('Failed to get available products: $e');
    }
  }

  // Purchase a product
  Future<bool> purchaseProduct(iap.ProductDetails product) async {
    if (_currentUserId == null) {
      throw Exception('User not authenticated');
    }

    try {
      final iap.PurchaseParam purchaseParam = iap.PurchaseParam(productDetails: product);
      
      bool success;
      if (Platform.isIOS) {
        success = await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      } else {
        success = await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      }

      return success;
    } catch (e) {
      throw Exception('Failed to purchase product: $e');
    }
  }

  // Restore purchases
  Future<void> restorePurchases() async {
    if (_currentUserId == null) {
      throw Exception('User not authenticated');
    }

    try {
      await _inAppPurchase.restorePurchases();
    } catch (e) {
      throw Exception('Failed to restore purchases: $e');
    }
  }

  // Handle purchase updates
  Future<void> _handlePurchaseUpdates(List<iap.PurchaseDetails> purchaseDetailsList) async {
    for (final iap.PurchaseDetails purchaseDetails in purchaseDetailsList) {
      await _processPurchase(purchaseDetails);
    }
  }

  // Process individual purchase
  Future<void> _processPurchase(iap.PurchaseDetails purchaseDetails) async {
    if (_currentUserId == null) return;

    try {
      AppPurchaseStatus status;
      switch (purchaseDetails.status) {
        case iap.PurchaseStatus.purchased:
          status = AppPurchaseStatus.completed;
          await _grantFullAccess();
          break;
        case iap.PurchaseStatus.restored:
          status = AppPurchaseStatus.completed;
          await _grantFullAccess();
          break;
        case iap.PurchaseStatus.error:
          status = AppPurchaseStatus.failed;
          break;
        case iap.PurchaseStatus.canceled:
          status = AppPurchaseStatus.cancelled;
          break;
        case iap.PurchaseStatus.pending:
          status = AppPurchaseStatus.pending;
          break;
      }

      // Record purchase in Firestore
      final purchase = Purchase(
        id: '',
        userId: _currentUserId!,
        productId: purchaseDetails.productID,
        productType: ProductType.fullAccess,
        status: status,
        purchaseDate: DateTime.now(),
        transactionId: purchaseDetails.purchaseID,
        receipt: purchaseDetails.verificationData.serverVerificationData,
        amount: Product.fullAccess.price,
        currency: Product.fullAccess.currency,
      );

      await _purchasesCollection.add(purchase.toFirestore());

      // Complete the purchase
      if (purchaseDetails.pendingCompletePurchase) {
        await _inAppPurchase.completePurchase(purchaseDetails);
      }
    } catch (e) {
      // Handle purchase processing error
    }
  }

  // Grant full access to user
  Future<void> _grantFullAccess() async {
    if (_currentUserId == null) return;

    try {
      final subscription = UserSubscription(
        userId: _currentUserId!,
        hasFullAccess: true,
        purchaseDate: DateTime.now(),
        purchasedProducts: ['full_access'],
        lastChecked: DateTime.now(),
      );

      await _subscriptionsCollection.doc(_currentUserId).set(subscription.toFirestore());
    } catch (e) {
      throw Exception('Failed to grant full access: $e');
    }
  }

  // Check user subscription status
  Future<UserSubscription> getUserSubscription() async {
    if (_currentUserId == null) {
      return UserSubscription(
        userId: '',
        lastChecked: DateTime.now(),
      );
    }

    try {
      final doc = await _subscriptionsCollection.doc(_currentUserId).get();
      
      if (doc.exists) {
        return UserSubscription.fromFirestore(doc);
      } else {
        // Create default subscription
        final subscription = UserSubscription(
          userId: _currentUserId!,
          lastChecked: DateTime.now(),
        );
        
        await _subscriptionsCollection.doc(_currentUserId).set(subscription.toFirestore());
        return subscription;
      }
    } catch (e) {
      throw Exception('Failed to get user subscription: $e');
    }
  }

  // Stream user subscription status
  Stream<UserSubscription> getUserSubscriptionStream() {
    if (_currentUserId == null) {
      return Stream.value(UserSubscription(
        userId: '',
        lastChecked: DateTime.now(),
      ));
    }

    return _subscriptionsCollection
        .doc(_currentUserId)
        .snapshots()
        .map((doc) {
          if (doc.exists) {
            return UserSubscription.fromFirestore(doc);
          } else {
            return UserSubscription(
              userId: _currentUserId!,
              lastChecked: DateTime.now(),
            );
          }
        });
  }

  // Get user's purchase history
  Future<List<Purchase>> getUserPurchases() async {
    if (_currentUserId == null) return [];

    try {
      final snapshot = await _purchasesCollection
          .where('userId', isEqualTo: _currentUserId)
          .orderBy('purchaseDate', descending: true)
          .get();

      return snapshot.docs.map((doc) => Purchase.fromFirestore(doc)).toList();
    } catch (e) {
      throw Exception('Failed to get purchase history: $e');
    }
  }

  // Validate purchase receipt (for additional security)
  Future<bool> validatePurchase(String receipt, String productId) async {
    try {
      // In a production app, you would validate the receipt with your backend
      // For now, we'll assume the purchase is valid if we have a receipt
      return receipt.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  // Check if user has made any purchases
  Future<bool> hasMadePurchase() async {
    if (_currentUserId == null) return false;

    try {
      final snapshot = await _purchasesCollection
          .where('userId', isEqualTo: _currentUserId)
          .where('status', isEqualTo: 'completed')
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  // Manual subscription grant (for testing or customer support)
  Future<void> grantFullAccessManually(String userId, String reason) async {
    try {
      final subscription = UserSubscription(
        userId: userId,
        hasFullAccess: true,
        purchaseDate: DateTime.now(),
        purchasedProducts: ['full_access'],
        lastChecked: DateTime.now(),
      );

      await _subscriptionsCollection.doc(userId).set(subscription.toFirestore());

      // Record the manual grant
      final purchase = Purchase(
        id: '',
        userId: userId,
        productId: 'full_access',
        productType: ProductType.fullAccess,
        status: AppPurchaseStatus.completed,
        purchaseDate: DateTime.now(),
        amount: 0.0,
        currency: 'USD',
        metadata: {'manual_grant': true, 'reason': reason},
      );

      await _purchasesCollection.add(purchase.toFirestore());
    } catch (e) {
      throw Exception('Failed to grant manual access: $e');
    }
  }
}
