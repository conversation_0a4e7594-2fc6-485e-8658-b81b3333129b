import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../domain/models/dog.dart';
import '../providers/dog_provider.dart';
import 'add_edit_dog_screen.dart';

class DogDetailScreen extends ConsumerWidget {
  final String dogId;

  const DogDetailScreen({super.key, required this.dogId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dogAsync = ref.watch(singleDogProvider(dogId));

    return Scaffold(
      body: dogAsync.when(
        data: (dog) {
          if (dog == null) {
            return const Center(
              child: Text('Dog not found'),
            );
          }
          return _buildDogDetail(context, ref, dog);
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, _) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'Failed to load dog: $error',
                textAlign: TextAlign.center,
                style: const TextStyle(color: Colors.red),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(singleDogProvider(dogId));
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDogDetail(BuildContext context, WidgetRef ref, Dog dog) {
    return CustomScrollView(
      slivers: [
        // App bar with photo
        SliverAppBar(
          expandedHeight: 300,
          pinned: true,
          flexibleSpace: FlexibleSpaceBar(
            title: Text(
              dog.name,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    offset: Offset(0, 1),
                    blurRadius: 3,
                    color: Colors.black54,
                  ),
                ],
              ),
            ),
            background: dog.photoUrl != null && dog.photoUrl!.isNotEmpty
                ? CachedNetworkImage(
                    imageUrl: dog.photoUrl!,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey[300],
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[300],
                      child: const Icon(
                        Icons.pets,
                        size: 80,
                        color: Colors.grey,
                      ),
                    ),
                  )
                : Container(
                    color: Colors.grey[300],
                    child: const Icon(
                      Icons.pets,
                      size: 80,
                      color: Colors.grey,
                    ),
                  ),
          ),
          actions: [
            PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'edit') {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => AddEditDogScreen(dogId: dog.id),
                    ),
                  );
                } else if (value == 'delete') {
                  _showDeleteDialog(context, ref, dog);
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit),
                      SizedBox(width: 8),
                      Text('Edit'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Delete', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        
        // Dog details
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Basic info card
                _buildInfoCard(
                  context,
                  'Basic Information',
                  [
                    _buildInfoRow('Breed', dog.breed),
                    _buildInfoRow('Gender', dog.gender == Gender.male ? 'Male' : 'Female'),
                    _buildInfoRow('Age', dog.ageString),
                    if (dog.microchipId != null)
                      _buildInfoRow('Microchip ID', dog.microchipId!),
                    if (dog.registrationNumber != null)
                      _buildInfoRow('Registration', dog.registrationNumber!),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Physical characteristics card
                if (dog.weight != null || dog.height != null || dog.color != null || dog.markings != null)
                  _buildInfoCard(
                    context,
                    'Physical Characteristics',
                    [
                      if (dog.weight != null)
                        _buildInfoRow('Weight', '${dog.weight} kg'),
                      if (dog.height != null)
                        _buildInfoRow('Height', '${dog.height} cm'),
                      if (dog.color != null)
                        _buildInfoRow('Color', dog.color!),
                      if (dog.markings != null)
                        _buildInfoRow('Markings', dog.markings!),
                    ],
                  ),
                const SizedBox(height: 16),
                
                // Health info card
                _buildInfoCard(
                  context,
                  'Health Information',
                  [
                    _buildInfoRow('Health Status', dog.healthStatus.name.toUpperCase()),
                    _buildInfoRow('Breeding Eligible', dog.isBreedingEligible ? 'Yes' : 'No'),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Notes card
                if (dog.notes != null && dog.notes!.isNotEmpty)
                  _buildInfoCard(
                    context,
                    'Notes',
                    [
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Text(
                          dog.notes!,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoCard(BuildContext context, String title, List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref, Dog dog) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Dog'),
        content: Text('Are you sure you want to delete ${dog.name}? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await ref.read(dogDeletionProvider.notifier).deleteDog(dog.id);
              
              final deletionState = ref.read(dogDeletionProvider);
              if (context.mounted) {
                if (deletionState.hasError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to delete dog: ${deletionState.error}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                } else {
                  Navigator.of(context).pop(); // Go back to dogs list
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Dog deleted successfully'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
