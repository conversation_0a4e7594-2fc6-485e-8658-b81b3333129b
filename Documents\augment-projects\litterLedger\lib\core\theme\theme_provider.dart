import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum AppThemeMode { light, dark, system }

class ThemeState {
  final AppThemeMode themeMode;
  final bool isLoading;

  const ThemeState({
    this.themeMode = AppThemeMode.system,
    this.isLoading = false,
  });

  ThemeState copyWith({
    AppThemeMode? themeMode,
    bool? isLoading,
  }) {
    return ThemeState(
      themeMode: themeMode ?? this.themeMode,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

class ThemeNotifier extends StateNotifier<ThemeState> {
  ThemeNotifier() : super(const ThemeState()) {
    _loadTheme();
  }

  static const String _themeKey = 'theme_mode';

  // Load theme from shared preferences
  Future<void> _loadTheme() async {
    state = state.copyWith(isLoading: true);
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeString = prefs.getString(_themeKey);
      
      AppThemeMode themeMode = AppThemeMode.system;
      if (themeString != null) {
        switch (themeString) {
          case 'light':
            themeMode = AppThemeMode.light;
            break;
          case 'dark':
            themeMode = AppThemeMode.dark;
            break;
          case 'system':
          default:
            themeMode = AppThemeMode.system;
            break;
        }
      }
      
      state = state.copyWith(themeMode: themeMode, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false);
    }
  }

  // Save theme to shared preferences
  Future<void> _saveTheme(AppThemeMode themeMode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String themeString;

      switch (themeMode) {
        case AppThemeMode.light:
          themeString = 'light';
          break;
        case AppThemeMode.dark:
          themeString = 'dark';
          break;
        case AppThemeMode.system:
          themeString = 'system';
          break;
      }

      await prefs.setString(_themeKey, themeString);
    } catch (e) {
      // Handle error silently
    }
  }

  // Set theme mode
  Future<void> setThemeMode(AppThemeMode themeMode) async {
    state = state.copyWith(themeMode: themeMode);
    await _saveTheme(themeMode);
  }

  // Toggle between light and dark (ignoring system)
  Future<void> toggleTheme() async {
    final newTheme = state.themeMode == AppThemeMode.light
        ? AppThemeMode.dark
        : AppThemeMode.light;
    await setThemeMode(newTheme);
  }

  // Get the actual theme mode based on system settings
  AppThemeMode getEffectiveThemeMode(BuildContext context) {
    if (state.themeMode == AppThemeMode.system) {
      final brightness = MediaQuery.of(context).platformBrightness;
      return brightness == Brightness.dark ? AppThemeMode.dark : AppThemeMode.light;
    }
    return state.themeMode;
  }

  // Check if current theme is dark
  bool isDarkMode(BuildContext context) {
    return getEffectiveThemeMode(context) == AppThemeMode.dark;
  }
}

// Theme provider
final themeProvider = StateNotifierProvider<ThemeNotifier, ThemeState>((ref) {
  return ThemeNotifier();
});

// Helper providers
final currentThemeModeProvider = Provider<AppThemeMode>((ref) {
  return ref.watch(themeProvider).themeMode;
});

final isThemeLoadingProvider = Provider<bool>((ref) {
  return ref.watch(themeProvider).isLoading;
});

// Provider to check if dark mode is active
final isDarkModeProvider = Provider.family<bool, BuildContext>((ref, context) {
  final themeNotifier = ref.watch(themeProvider.notifier);
  return themeNotifier.isDarkMode(context);
});

// Provider for effective theme mode
final effectiveThemeModeProvider = Provider.family<AppThemeMode, BuildContext>((ref, context) {
  final themeNotifier = ref.watch(themeProvider.notifier);
  return themeNotifier.getEffectiveThemeMode(context);
});
