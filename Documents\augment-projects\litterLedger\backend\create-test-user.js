const bcrypt = require('bcryptjs');
const { User, sequelize } = require('./models');

async function createTestUser() {
  try {
    // Connect to database
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Sync models
    await sequelize.sync();
    console.log('Database models synced.');

    // Check if test user already exists
    const existingUser = await User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('✅ Test user already exists:', existingUser.email);
      return;
    }

    // Create test user
    const hashedPassword = await bcrypt.hash('password123', 10);
    
    const testUser = await User.create({
      email: '<EMAIL>',
      password: hashedPassword,
      display_name: 'Test User',
      first_name: 'Test',
      last_name: 'User',
      is_premium: false,
      is_verified: true
    });

    console.log('✅ Test user created successfully:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: password123');
    console.log('   ID:', testUser.id);

  } catch (error) {
    console.error('❌ Error creating test user:', error);
  } finally {
    await sequelize.close();
  }
}

createTestUser();
