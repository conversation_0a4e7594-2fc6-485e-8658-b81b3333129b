# OAuth Setup Instructions

This document provides step-by-step instructions for setting up real Google and Apple OAuth authentication for LitterLedger.

## 🔧 Google OAuth Setup

### Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing project
3. Enable the Google+ API and Google Sign-In API

### Step 2: Configure OAuth Consent Screen

1. Go to **APIs & Services** > **OAuth consent screen**
2. Choose **External** user type
3. Fill in required information:
   - App name: `LitterLedger`
   - User support email: Your email
   - Developer contact information: Your email
4. Add scopes: `email`, `profile`
5. Add test users if needed

### Step 3: Create OAuth Credentials

1. Go to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth client ID**
3. Create credentials for each platform:

#### Web Application
- Application type: **Web application**
- Name: `LitterLedger Web`
- Authorized JavaScript origins: `http://localhost:54311` (or your dev server)
- Authorized redirect URIs: `http://localhost:54311/auth/google/callback`

#### Android Application
- Application type: **Android**
- Name: `LitterLedger Android`
- Package name: `com.yourcompany.litter_ledger`
- SHA-1 certificate fingerprint: (Get from your keystore)

#### iOS Application
- Application type: **iOS**
- Name: `LitterLedger iOS`
- Bundle ID: `com.yourcompany.litterLedger`

### Step 4: Update Configuration

1. Replace the client ID in `lib/core/services/auth_service.dart`:
   ```dart
   clientId: kIsWeb ? 'YOUR_WEB_CLIENT_ID.apps.googleusercontent.com' : null,
   ```

2. Update `web/index.html`:
   ```javascript
   client_id: 'YOUR_WEB_CLIENT_ID.apps.googleusercontent.com',
   ```

3. For Android, add to `android/app/src/main/res/values/strings.xml`:
   ```xml
   <string name="default_web_client_id">YOUR_WEB_CLIENT_ID.apps.googleusercontent.com</string>
   ```

4. For iOS, add to `ios/Runner/Info.plist`:
   ```xml
   <key>CFBundleURLTypes</key>
   <array>
       <dict>
           <key>CFBundleURLName</key>
           <string>REVERSED_CLIENT_ID</string>
           <key>CFBundleURLSchemes</key>
           <array>
               <string>YOUR_REVERSED_CLIENT_ID</string>
           </array>
       </dict>
   </array>
   ```

## 🍎 Apple Sign-In Setup

### Step 1: Apple Developer Account

1. You need an active Apple Developer account ($99/year)
2. Go to [Apple Developer Console](https://developer.apple.com/)

### Step 2: Configure App ID

1. Go to **Certificates, Identifiers & Profiles**
2. Click **Identifiers** > **App IDs**
3. Create new App ID or edit existing:
   - Bundle ID: `com.yourcompany.litterLedger`
   - Enable **Sign In with Apple** capability

### Step 3: Configure Service ID (for Web)

1. Go to **Identifiers** > **Services IDs**
2. Create new Service ID:
   - Identifier: `com.yourcompany.litterledger.signin`
   - Description: `LitterLedger Sign In`
3. Configure **Sign In with Apple**:
   - Primary App ID: Select your app ID
   - Web Domain: Your domain (for production)
   - Return URLs: `https://yourdomain.com/auth/apple/callback`

### Step 4: Update Configuration

1. Update `web/index.html`:
   ```javascript
   clientId: 'com.yourcompany.litterledger.signin',
   ```

2. For iOS, no additional configuration needed (uses App ID)

3. For Android, add to `android/app/build.gradle`:
   ```gradle
   android {
       defaultConfig {
           // Add your bundle ID
           applicationId "com.yourcompany.litter_ledger"
       }
   }
   ```

## 🚀 Testing OAuth

### Development Testing

1. **Google Sign-In**: Works immediately after configuration
2. **Apple Sign-In**: 
   - iOS: Works on device with Apple ID
   - Web: Requires HTTPS in production
   - Android: Requires additional setup

### Production Deployment

1. **Update domains**: Replace localhost with your production domain
2. **HTTPS required**: Both Google and Apple require HTTPS in production
3. **App Store**: Apple Sign-In is required if you offer any other social sign-in

## 🔒 Security Notes

1. **Never commit credentials**: Keep client IDs and secrets secure
2. **Use environment variables**: Store sensitive data in environment variables
3. **Validate tokens**: Always validate OAuth tokens on your backend
4. **Refresh tokens**: Implement token refresh logic for long-term sessions

## 📱 Platform-Specific Notes

### Web
- Google: Works with Google Identity Services
- Apple: Requires Apple JS SDK and HTTPS

### iOS
- Google: Requires URL scheme configuration
- Apple: Built-in support, works seamlessly

### Android
- Google: Requires SHA-1 fingerprint
- Apple: Requires additional setup and testing

## 🛠️ Current Implementation Status

- ✅ **Backend**: Social auth endpoint ready
- ✅ **Flutter**: OAuth plugins integrated
- ✅ **UI**: Social sign-in buttons implemented
- ⚠️ **Configuration**: Requires real OAuth credentials
- ⚠️ **Testing**: Currently shows configuration errors

## 📞 Support

For OAuth setup issues:
1. Check Google Cloud Console for API quotas and errors
2. Verify Apple Developer Console configuration
3. Test with simple OAuth flows first
4. Check platform-specific documentation

---

**Note**: The current implementation will show configuration errors until real OAuth credentials are added. This is expected behavior for security reasons.
