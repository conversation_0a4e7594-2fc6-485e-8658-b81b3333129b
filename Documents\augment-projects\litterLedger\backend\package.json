{"name": "litterledger-backend", "version": "1.0.0", "description": "Self-hosted backend for LitterLedger dog breeding management app", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js"}, "keywords": ["dog", "breeding", "management", "api", "nodejs"], "author": "LitterLedger Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "socket.io": "^4.7.4", "sqlite3": "^5.1.6", "sequelize": "^6.35.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "compression": "^1.7.4", "express-slow-down": "^2.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=16.0.0"}}