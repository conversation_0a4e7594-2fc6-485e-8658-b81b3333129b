import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../domain/models/dog.dart';

class DogCard extends StatelessWidget {
  final Dog dog;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const DogCard({
    super.key,
    required this.dog,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Dog photo
              _buildDogPhoto(),
              const SizedBox(width: 16),
              
              // Dog info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name and gender
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            dog.name,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Icon(
                          dog.gender == Gender.male ? Icons.male : Icons.female,
                          color: dog.gender == Gender.male ? Colors.blue : Colors.pink,
                          size: 20,
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    
                    // Breed
                    Text(
                      dog.breed,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                    const SizedBox(height: 4),
                    
                    // Age
                    Text(
                      dog.ageString,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[500],
                          ),
                    ),
                    const SizedBox(height: 8),
                    
                    // Tags and status
                    Row(
                      children: [
                        // Breeding eligible badge
                        if (dog.isBreedingEligible)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.green[100],
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'Breeding',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.green[800],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        
                        // Health status badge
                        if (dog.healthStatus != HealthStatus.unknown) ...[
                          if (dog.isBreedingEligible) const SizedBox(width: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: _getHealthStatusColor(dog.healthStatus).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              dog.healthStatus.name.toUpperCase(),
                              style: TextStyle(
                                fontSize: 10,
                                color: _getHealthStatusColor(dog.healthStatus),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                        
                        const Spacer(),
                        
                        // Action buttons
                        if (onEdit != null || onDelete != null)
                          PopupMenuButton<String>(
                            onSelected: (value) {
                              if (value == 'edit' && onEdit != null) {
                                onEdit!();
                              } else if (value == 'delete' && onDelete != null) {
                                onDelete!();
                              }
                            },
                            itemBuilder: (context) => [
                              if (onEdit != null)
                                const PopupMenuItem(
                                  value: 'edit',
                                  child: Row(
                                    children: [
                                      Icon(Icons.edit, size: 16),
                                      SizedBox(width: 8),
                                      Text('Edit'),
                                    ],
                                  ),
                                ),
                              if (onDelete != null)
                                const PopupMenuItem(
                                  value: 'delete',
                                  child: Row(
                                    children: [
                                      Icon(Icons.delete, size: 16, color: Colors.red),
                                      SizedBox(width: 8),
                                      Text('Delete', style: TextStyle(color: Colors.red)),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDogPhoto() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[200],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: dog.photoUrl != null && dog.photoUrl!.isNotEmpty
            ? CachedNetworkImage(
                imageUrl: dog.photoUrl!,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey[200],
                  child: const Center(
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[200],
                  child: Icon(
                    Icons.pets,
                    size: 32,
                    color: Colors.grey[400],
                  ),
                ),
              )
            : Icon(
                Icons.pets,
                size: 32,
                color: Colors.grey[400],
              ),
      ),
    );
  }

  Color _getHealthStatusColor(HealthStatus status) {
    switch (status) {
      case HealthStatus.excellent:
        return Colors.green;
      case HealthStatus.good:
        return Colors.lightGreen;
      case HealthStatus.fair:
        return Colors.orange;
      case HealthStatus.poor:
        return Colors.red;
      case HealthStatus.unknown:
        return Colors.grey;
    }
  }
}
