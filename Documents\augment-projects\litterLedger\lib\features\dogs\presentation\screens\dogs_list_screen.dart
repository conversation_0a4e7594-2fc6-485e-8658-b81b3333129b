import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../monetization/presentation/providers/purchase_provider.dart';
import '../../../monetization/presentation/widgets/paywall_wrapper.dart';
import '../../domain/models/dog.dart';
import '../providers/dog_provider.dart';
import 'add_edit_dog_screen.dart';
import 'dog_detail_screen.dart';
import '../widgets/dog_card.dart';

class DogsListScreen extends ConsumerStatefulWidget {
  const DogsListScreen({super.key});

  @override
  ConsumerState<DogsListScreen> createState() => _DogsListScreenState();
}

class _DogsListScreenState extends ConsumerState<DogsListScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dogsAsync = ref.watch(dogsProvider);
    final searchResults = ref.watch(dogSearchProvider);

    return Scaffold(
      appBar: AppBar(
        title: _isSearching
            ? TextField(
                controller: _searchController,
                autofocus: true,
                decoration: const InputDecoration(
                  hintText: 'Search dogs...',
                  border: InputBorder.none,
                ),
                onChanged: (query) {
                  ref.read(dogSearchProvider.notifier).searchDogs(query);
                },
              )
            : const Text('My Dogs'),
        actions: [
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                  ref.read(dogSearchProvider.notifier).clearSearch();
                }
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AddEditDogScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: _isSearching
          ? _buildSearchResults(searchResults)
          : _buildDogsList(dogsAsync),
    );
  }

  Widget _buildSearchResults(AsyncValue<List<Dog>> searchResults) {
    return searchResults.when(
      data: (dogs) {
        if (dogs.isEmpty && _searchController.text.isNotEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.search_off, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'No dogs found',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
              ],
            ),
          );
        }
        
        if (dogs.isEmpty) {
          return const Center(
            child: Text(
              'Start typing to search your dogs',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: dogs.length,
          itemBuilder: (context, index) {
            return DogCard(
              dog: dogs[index],
              onTap: () => _navigateToDogDetail(dogs[index]),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, _) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Search failed: $error',
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.red),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDogsList(AsyncValue<List<Dog>> dogsAsync) {
    return dogsAsync.when(
      data: (dogs) {
        if (dogs.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(dogsProvider);
          },
          child: _buildDogsListView(dogs),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, _) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Failed to load dogs: $error',
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.red),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.invalidate(dogsProvider);
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.pets,
            size: 120,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 24),
          Text(
            'No dogs yet',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add your first dog to get started',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey[500],
                ),
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AddEditDogScreen(),
                ),
              );
            },
            icon: const Icon(Icons.add),
            label: const Text('Add Your First Dog'),
          ),
        ],
      ),
    );
  }

  Widget _buildDogsListView(List<Dog> dogs) {
    return Consumer(
      builder: (context, ref, child) {
        final hasPremiumAccess = ref.watch(hasPremiumAccessProvider);
        const int freeLimit = 3; // Free users can have up to 3 dogs

        if (!hasPremiumAccess && dogs.length > freeLimit) {
      // Show limited list with upgrade prompt
      final limitedDogs = dogs.take(freeLimit).toList();

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: limitedDogs.length + 1, // +1 for upgrade prompt
        itemBuilder: (context, index) {
          if (index < limitedDogs.length) {
            return DogCard(
              dog: limitedDogs[index],
              onTap: () => _navigateToDogDetail(limitedDogs[index]),
            );
          } else {
            // Show upgrade prompt
            return PremiumFeatureGate(
              featureName: 'Unlimited Dogs',
              child: const SizedBox.shrink(),
            );
          }
        },
      );
    }

    // Show all dogs for premium users or if under limit
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: dogs.length,
      itemBuilder: (context, index) {
        return DogCard(
          dog: dogs[index],
          onTap: () => _navigateToDogDetail(dogs[index]),
        );
      },
        );
      },
    );
  }

  void _navigateToDogDetail(Dog dog) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DogDetailScreen(dogId: dog.id),
      ),
    );
  }
}
