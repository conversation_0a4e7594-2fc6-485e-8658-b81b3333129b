# LitterLedger Backend Configuration
# Generated on 2025-06-22T05:01:29.392Z

# Server Configuration
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# Database Configuration (SQLite for development)
DB_STORAGE=./database.sqlite

# JWT Configuration (Auto-generated secure keys)
JWT_SECRET=691f8080f33b0f21ab4d8eeffeee9808f37b4c5e759ba3ca5de6a7b3c8c61a4e2475bb0da39b713f019653d7c27bb4108ab90c47fc3f6df37a170ff95c770411
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=c58308785b0adf6ba875ec26cedd4e6da9c5e42178051e8fdde0d75ef430d76ccedd845bf608d9c18e2ba2dcf25b7f0a56c993fab26a2a321da1fe5f91942eb5

# File Upload Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# Development
DEBUG=litterledger:*

# Google OAuth (Optional - configure if needed)
# GOOGLE_CLIENT_ID=your_google_client_id
# GOOGLE_CLIENT_SECRET=your_google_client_secret
# GOOGLE_CALLBACK_URL=http://localhost:3001/api/auth/google/callback

# Email Configuration (Optional - for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your_app_password
