# 🚀 LitterLedger Self-Hosted Backend Setup

## Overview

I've created a complete Node.js backend for LitterLedger that you can self-host! This gives you full control over your data and eliminates the need for Firebase.

## ✨ Features

### 🔐 **Authentication**
- JWT-based authentication
- Google OAuth support
- Secure password hashing
- Token refresh mechanism

### 🐕 **Dog Management**
- Complete CRUD operations
- Photo uploads with automatic resizing
- Health records tracking
- Breeding information
- Search and filtering

### 💬 **Real-time Messaging**
- Socket.IO for real-time chat
- Direct messaging between breeders
- Message history
- Typing indicators
- Unread message counts

### 💎 **Premium Features**
- One-time purchase model
- Feature gating for free vs premium users
- Unlimited dogs for premium users

### 📁 **File Management**
- Local file storage
- Automatic image optimization
- Multiple image sizes (thumbnail, medium, large)
- Secure file access

## 🚀 Quick Start

### Step 1: Install Node.js
1. Download from [nodejs.org](https://nodejs.org/)
2. Install version 16 or higher
3. Verify installation: `node --version`

### Step 2: Set Up Backend
```bash
# Navigate to backend directory
cd Documents\augment-projects\litterLedger\backend

# Run the setup script (Windows)
start.bat

# Or manually:
npm install
node setup.js
npm run dev
```

### Step 3: Verify Installation
1. **Open browser**: http://localhost:3001/health
2. **Should see**: `{"status":"OK","timestamp":"...","version":"1.0.0"}`
3. **Server logs**: Should show "🚀 LitterLedger Backend Server running on port 3001"

## 📋 What Gets Created

### Directory Structure
```
backend/
├── server.js              # Main server file
├── package.json           # Dependencies
├── .env                   # Configuration (auto-generated)
├── database.sqlite        # SQLite database (auto-created)
├── uploads/               # File storage directory
├── logs/                  # Log files
├── models/                # Database models
├── routes/                # API endpoints
└── middleware/            # Authentication & security
```

### Database Tables
- **Users**: User accounts and profiles
- **Dogs**: Dog profiles and breeding data
- **Messages**: Chat messages
- **Chats**: Conversation threads
- **HealthRecords**: Veterinary and health data

## 🔧 Configuration

The setup script creates a `.env` file with secure defaults:

```env
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000
DB_STORAGE=./database.sqlite
JWT_SECRET=<auto-generated-secure-key>
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=********
```

## 🌐 API Endpoints

### Authentication
- `POST /api/auth/register` - Create account
- `POST /api/auth/login` - Login
- `POST /api/auth/google` - Google OAuth
- `GET /api/auth/me` - Get current user

### Dogs
- `GET /api/dogs` - Get user's dogs
- `POST /api/dogs` - Create new dog
- `PUT /api/dogs/:id` - Update dog
- `DELETE /api/dogs/:id` - Delete dog

### Messages (Premium)
- `GET /api/messages/chats` - Get conversations
- `POST /api/messages/send` - Send message
- `GET /api/messages/chats/:id/messages` - Get messages

### File Uploads
- `POST /api/uploads/dog-photos` - Upload dog photos
- `POST /api/uploads/profile-photo` - Upload profile photo

## 📱 Connecting Flutter App

I've created an API service for Flutter to connect to your backend:

### Update Flutter App
1. **Add HTTP dependency** to `pubspec.yaml`:
```yaml
dependencies:
  http: ^1.1.0
  shared_preferences: ^2.2.2
```

2. **Use the API service** (already created in `lib/core/services/api_service.dart`):
```dart
// Initialize API service
final apiService = ref.read(apiServiceProvider);

// Login user
final authResponse = await apiService.login(
  email: '<EMAIL>',
  password: 'password123',
);

// Get dogs
final dogs = await apiService.getDogs();

// Create dog
final newDog = await apiService.createDog({
  'name': 'Buddy',
  'breed': 'Golden Retriever',
  'gender': 'male',
});
```

## 🔒 Security Features

- **JWT Authentication**: Secure token-based auth
- **Rate Limiting**: Prevents abuse
- **Input Validation**: Protects against injection
- **File Type Restrictions**: Only allows safe file types
- **CORS Protection**: Configurable cross-origin requests
- **Helmet Security**: HTTP security headers

## 🚀 Production Deployment

### Option 1: Traditional Server
```bash
# On your server
git clone <your-repo>
cd backend
npm ci --only=production
npm install -g pm2
pm2 start server.js --name litterledger
pm2 startup
pm2 save
```

### Option 2: Docker
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

### Option 3: Cloud Platforms
- **Heroku**: Easy deployment with git
- **DigitalOcean**: App Platform or Droplets
- **AWS**: EC2, Elastic Beanstalk, or Lambda
- **Google Cloud**: App Engine or Compute Engine

## 📊 Monitoring

### Health Check
```bash
curl http://localhost:3001/health
```

### Logs
```bash
# View logs
tail -f logs/app.log

# PM2 logs (if using PM2)
pm2 logs litterledger
```

## 🔧 Customization

### Add New Features
1. **Create model** in `models/`
2. **Add routes** in `routes/`
3. **Update API service** in Flutter
4. **Test endpoints**

### Database Changes
- Uses Sequelize ORM
- Automatic migrations
- SQLite for development, PostgreSQL for production

## 🆘 Troubleshooting

### Common Issues

1. **Port 3001 already in use**
   ```bash
   # Change port in .env
   PORT=3002
   ```

2. **Database errors**
   ```bash
   # Delete and recreate database
   rm database.sqlite
   npm run dev
   ```

3. **File upload errors**
   ```bash
   # Check permissions
   chmod 755 uploads/
   ```

4. **JWT errors**
   ```bash
   # Regenerate secrets
   node setup.js
   ```

## 🎯 Next Steps

1. **Start the backend**: Run `start.bat` in backend folder
2. **Test API**: Visit http://localhost:3001/health
3. **Update Flutter**: Connect app to your backend
4. **Create account**: Register through the app
5. **Add dogs**: Test full functionality
6. **Deploy**: Move to production server when ready

## 💡 Benefits of Self-Hosting

- ✅ **Full data control**: Your data stays on your servers
- ✅ **No vendor lock-in**: Not dependent on Firebase
- ✅ **Cost effective**: No per-user Firebase costs
- ✅ **Customizable**: Add features as needed
- ✅ **Privacy**: Complete data privacy
- ✅ **Scalable**: Scale as your user base grows

## 📞 Support

If you need help:
1. Check the logs for error messages
2. Review the API documentation in `backend/README.md`
3. Test endpoints with Postman or curl
4. Check database with SQLite browser

---

**🎉 You now have a complete, self-hosted backend for LitterLedger!**

The backend is production-ready and includes all the features needed for a professional dog breeding management application.
