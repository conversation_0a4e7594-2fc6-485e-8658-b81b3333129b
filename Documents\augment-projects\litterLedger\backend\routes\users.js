const express = require('express');
const { body, validationResult } = require('express-validator');
const { User, Dog } = require('../models');
const { authenticateToken, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// Get user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const user = await User.findByPk(req.user.userId, {
      include: [
        {
          model: Dog,
          as: 'dogs',
          attributes: ['id', 'name', 'breed', 'photos']
        }
      ]
    });

    if (!user) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    res.json({ user: user.toJSON() });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      error: 'Failed to fetch profile'
    });
  }
});

// Update user profile
router.put('/profile', [
  authenticateToken,
  body('display_name').optional().trim().isLength({ min: 1 }),
  body('email').optional().isEmail().normalizeEmail(),
  body('phone').optional().isMobilePhone(),
  body('website').optional().isURL()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const user = await User.findByPk(req.user.userId);
    if (!user) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    // Check if email is being changed and if it's already taken
    if (req.body.email && req.body.email !== user.email) {
      const existingUser = await User.findOne({
        where: { email: req.body.email }
      });
      
      if (existingUser) {
        return res.status(409).json({
          error: 'Email already taken'
        });
      }
    }

    await user.update(req.body);

    res.json({
      message: 'Profile updated successfully',
      user: user.toJSON()
    });

  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      error: 'Failed to update profile'
    });
  }
});

// Get public user profile
router.get('/:id/public', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findOne({
      where: { 
        id,
        is_active: true
      },
      attributes: [
        'id', 
        'display_name', 
        'kennel_name', 
        'location', 
        'bio', 
        'website',
        'profile_photo_url',
        'created_at'
      ],
      include: [
        {
          model: Dog,
          as: 'dogs',
          where: { is_public: true },
          required: false,
          attributes: ['id', 'name', 'breed', 'photos', 'gender', 'birth_date']
        }
      ]
    });

    if (!user) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    // Check privacy settings
    if (!user.preferences?.privacy?.profile_visible) {
      return res.status(403).json({
        error: 'Profile is private'
      });
    }

    res.json({ user });

  } catch (error) {
    console.error('Get public profile error:', error);
    res.status(500).json({
      error: 'Failed to fetch public profile'
    });
  }
});

// Search users/breeders
router.get('/search', optionalAuth, async (req, res) => {
  try {
    const { q, location, page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    if (!q || q.trim().length < 2) {
      return res.status(400).json({
        error: 'Search query must be at least 2 characters'
      });
    }

    const where = {
      is_active: true,
      '$preferences.privacy.profile_visible$': true
    };

    // Add search conditions
    const searchConditions = [];
    
    if (q) {
      searchConditions.push(
        { display_name: { [Op.iLike]: `%${q}%` } },
        { kennel_name: { [Op.iLike]: `%${q}%` } }
      );
    }

    if (location) {
      searchConditions.push(
        { location: { [Op.iLike]: `%${location}%` } }
      );
    }

    if (searchConditions.length > 0) {
      where[Op.or] = searchConditions;
    }

    const users = await User.findAndCountAll({
      where,
      attributes: [
        'id',
        'display_name',
        'kennel_name',
        'location',
        'bio',
        'profile_photo_url',
        'created_at'
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['display_name', 'ASC']],
      include: [
        {
          model: Dog,
          as: 'dogs',
          where: { is_public: true },
          required: false,
          attributes: ['id', 'breed'],
          limit: 3
        }
      ]
    });

    res.json({
      users: users.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: users.count,
        pages: Math.ceil(users.count / limit)
      }
    });

  } catch (error) {
    console.error('Search users error:', error);
    res.status(500).json({
      error: 'Failed to search users'
    });
  }
});

// Update user preferences
router.put('/preferences', [
  authenticateToken,
  body('preferences').isObject()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const user = await User.findByPk(req.user.userId);
    if (!user) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    // Merge with existing preferences
    const updatedPreferences = {
      ...user.preferences,
      ...req.body.preferences
    };

    await user.update({ preferences: updatedPreferences });

    res.json({
      message: 'Preferences updated successfully',
      preferences: updatedPreferences
    });

  } catch (error) {
    console.error('Update preferences error:', error);
    res.status(500).json({
      error: 'Failed to update preferences'
    });
  }
});

// Upgrade to premium
router.post('/upgrade-premium', authenticateToken, async (req, res) => {
  try {
    const { purchase_token, platform } = req.body;

    // In a real implementation, you'd verify the purchase with Apple/Google
    // For demo purposes, we'll just upgrade the user
    
    const user = await User.findByPk(req.user.userId);
    if (!user) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    if (user.isPremium()) {
      return res.status(400).json({
        error: 'User is already premium'
      });
    }

    // Upgrade to premium (lifetime for one-time purchase)
    await user.update({
      is_premium: true,
      premium_expires_at: null // Lifetime premium
    });

    res.json({
      message: 'Successfully upgraded to premium',
      user: user.toJSON()
    });

  } catch (error) {
    console.error('Premium upgrade error:', error);
    res.status(500).json({
      error: 'Failed to upgrade to premium'
    });
  }
});

// Delete account
router.delete('/account', authenticateToken, async (req, res) => {
  try {
    const { password } = req.body;

    const user = await User.findByPk(req.user.userId);
    if (!user) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    // Verify password for security
    if (user.password && !await user.validatePassword(password)) {
      return res.status(401).json({
        error: 'Invalid password'
      });
    }

    // Soft delete - deactivate account
    await user.update({
      is_active: false,
      email: `deleted_${Date.now()}_${user.email}` // Prevent email conflicts
    });

    res.json({
      message: 'Account deleted successfully'
    });

  } catch (error) {
    console.error('Delete account error:', error);
    res.status(500).json({
      error: 'Failed to delete account'
    });
  }
});

module.exports = router;
