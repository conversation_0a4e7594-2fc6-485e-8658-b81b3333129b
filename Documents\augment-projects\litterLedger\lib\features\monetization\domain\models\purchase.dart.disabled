import 'package:cloud_firestore/cloud_firestore.dart';

enum AppPurchaseStatus { pending, completed, failed, cancelled, restored }

enum ProductType { fullAccess }

class Purchase {
  final String id;
  final String userId;
  final String productId;
  final ProductType productType;
  final AppPurchaseStatus status;
  final DateTime purchaseDate;
  final String? transactionId;
  final String? receipt;
  final double amount;
  final String currency;
  final Map<String, dynamic>? metadata;

  const Purchase({
    required this.id,
    required this.userId,
    required this.productId,
    required this.productType,
    required this.status,
    required this.purchaseDate,
    this.transactionId,
    this.receipt,
    required this.amount,
    required this.currency,
    this.metadata,
  });

  // Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'productId': productId,
      'productType': productType.name,
      'status': status.name,
      'purchaseDate': purchaseDate.millisecondsSinceEpoch,
      'transactionId': transactionId,
      'receipt': receipt,
      'amount': amount,
      'currency': currency,
      'metadata': metadata,
    };
  }

  // Create from Firestore document
  factory Purchase.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return Purchase(
      id: doc.id,
      userId: data['userId'] ?? '',
      productId: data['productId'] ?? '',
      productType: ProductType.values.firstWhere(
        (t) => t.name == data['productType'],
        orElse: () => ProductType.fullAccess,
      ),
      status: AppPurchaseStatus.values.firstWhere(
        (s) => s.name == data['status'],
        orElse: () => AppPurchaseStatus.pending,
      ),
      purchaseDate: DateTime.fromMillisecondsSinceEpoch(data['purchaseDate']),
      transactionId: data['transactionId'],
      receipt: data['receipt'],
      amount: (data['amount'] ?? 0.0).toDouble(),
      currency: data['currency'] ?? 'USD',
      metadata: data['metadata'] != null 
          ? Map<String, dynamic>.from(data['metadata'])
          : null,
    );
  }

  // Copy with method
  Purchase copyWith({
    AppPurchaseStatus? status,
    String? transactionId,
    String? receipt,
    Map<String, dynamic>? metadata,
  }) {
    return Purchase(
      id: id,
      userId: userId,
      productId: productId,
      productType: productType,
      status: status ?? this.status,
      purchaseDate: purchaseDate,
      transactionId: transactionId ?? this.transactionId,
      receipt: receipt ?? this.receipt,
      amount: amount,
      currency: currency,
      metadata: metadata ?? this.metadata,
    );
  }
}

class Product {
  final String id;
  final ProductType type;
  final String title;
  final String description;
  final double price;
  final String currency;
  final List<String> features;
  final bool isAvailable;

  const Product({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.price,
    required this.currency,
    required this.features,
    this.isAvailable = true,
  });

  // Predefined products
  static const Product fullAccess = Product(
    id: 'full_access',
    type: ProductType.fullAccess,
    title: 'LitterLedger Full Access',
    description: 'Unlock all features of LitterLedger with a one-time purchase',
    price: 2.99,
    currency: 'USD',
    features: [
      'Unlimited dog profiles',
      'Advanced breeding records',
      'In-app messaging with other breeders',
      'Photo storage and management',
      'Health tracking and records',
      'Pedigree management',
      'Genetic trait tracking',
      'Offline access to your data',
      'No ads, ever',
      'Priority customer support',
    ],
  );

  String get formattedPrice => '\$${price.toStringAsFixed(2)}';
}

class UserSubscription {
  final String userId;
  final bool hasFullAccess;
  final DateTime? purchaseDate;
  final DateTime? expiryDate;
  final List<String> purchasedProducts;
  final DateTime lastChecked;

  const UserSubscription({
    required this.userId,
    this.hasFullAccess = false,
    this.purchaseDate,
    this.expiryDate,
    this.purchasedProducts = const [],
    required this.lastChecked,
  });

  bool get isActive {
    if (!hasFullAccess) return false;
    if (expiryDate == null) return true; // One-time purchase
    return DateTime.now().isBefore(expiryDate!);
  }

  // Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'hasFullAccess': hasFullAccess,
      'purchaseDate': purchaseDate?.millisecondsSinceEpoch,
      'expiryDate': expiryDate?.millisecondsSinceEpoch,
      'purchasedProducts': purchasedProducts,
      'lastChecked': lastChecked.millisecondsSinceEpoch,
    };
  }

  // Create from Firestore document
  factory UserSubscription.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return UserSubscription(
      userId: doc.id,
      hasFullAccess: data['hasFullAccess'] ?? false,
      purchaseDate: data['purchaseDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(data['purchaseDate'])
          : null,
      expiryDate: data['expiryDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(data['expiryDate'])
          : null,
      purchasedProducts: List<String>.from(data['purchasedProducts'] ?? []),
      lastChecked: data['lastChecked'] != null
          ? DateTime.fromMillisecondsSinceEpoch(data['lastChecked'])
          : DateTime.now(),
    );
  }

  // Copy with method
  UserSubscription copyWith({
    bool? hasFullAccess,
    DateTime? purchaseDate,
    DateTime? expiryDate,
    List<String>? purchasedProducts,
    DateTime? lastChecked,
  }) {
    return UserSubscription(
      userId: userId,
      hasFullAccess: hasFullAccess ?? this.hasFullAccess,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      expiryDate: expiryDate ?? this.expiryDate,
      purchasedProducts: purchasedProducts ?? this.purchasedProducts,
      lastChecked: lastChecked ?? this.lastChecked,
    );
  }
}
