import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ApiService {
  static const String baseUrl = 'http://localhost:3001/api';
  
  String? _token;
  
  // Headers for requests
  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
    };
    
    if (_token != null) {
      headers['Authorization'] = 'Bearer $_token';
    }
    
    return headers;
  }

  // Set authentication token
  void setToken(String token) {
    _token = token;
  }

  // Clear authentication token
  void clearToken() {
    _token = null;
  }

  // Generic GET request
  Future<Map<String, dynamic>> get(String endpoint) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl$endpoint'),
        headers: _headers,
      );
      
      return _handleResponse(response);
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  // Generic POST request
  Future<Map<String, dynamic>> post(String endpoint, Map<String, dynamic> data) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl$endpoint'),
        headers: _headers,
        body: json.encode(data),
      );
      
      return _handleResponse(response);
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  // Generic PUT request
  Future<Map<String, dynamic>> put(String endpoint, Map<String, dynamic> data) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl$endpoint'),
        headers: _headers,
        body: json.encode(data),
      );
      
      return _handleResponse(response);
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  // Generic DELETE request
  Future<Map<String, dynamic>> delete(String endpoint) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl$endpoint'),
        headers: _headers,
      );
      
      return _handleResponse(response);
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  // Handle HTTP response
  Map<String, dynamic> _handleResponse(http.Response response) {
    final data = json.decode(response.body) as Map<String, dynamic>;
    
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return data;
    } else {
      throw ApiException(
        data['message'] ?? data['error'] ?? 'Unknown error',
        statusCode: response.statusCode,
      );
    }
  }

  // Authentication methods
  Future<AuthResponse> register({
    required String email,
    required String password,
    required String displayName,
    String? firstName,
    String? lastName,
  }) async {
    final data = await post('/auth/register', {
      'email': email,
      'password': password,
      'display_name': displayName,
      if (firstName != null) 'first_name': firstName,
      if (lastName != null) 'last_name': lastName,
    });
    
    final token = data['token'] as String;
    setToken(token);
    await _saveToken(token);
    
    return AuthResponse.fromJson(data);
  }

  Future<AuthResponse> login({
    required String email,
    required String password,
  }) async {
    final data = await post('/auth/login', {
      'email': email,
      'password': password,
    });
    
    final token = data['token'] as String;
    setToken(token);
    await _saveToken(token);
    
    return AuthResponse.fromJson(data);
  }

  Future<AuthResponse> googleLogin({
    required String googleToken,
    required Map<String, dynamic> userInfo,
  }) async {
    final data = await post('/auth/google', {
      'google_token': googleToken,
      'user_info': userInfo,
    });
    
    final token = data['token'] as String;
    setToken(token);
    await _saveToken(token);
    
    return AuthResponse.fromJson(data);
  }

  Future<void> logout() async {
    try {
      await post('/auth/logout', {});
    } catch (e) {
      // Ignore logout errors
    }
    
    clearToken();
    await _removeToken();
  }

  Future<User> getCurrentUser() async {
    final data = await get('/auth/me');
    return User.fromJson(data['user']);
  }

  // Dog methods
  Future<List<Dog>> getDogs({
    int page = 1,
    int limit = 20,
    String? search,
    String? breed,
    String? gender,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'limit': limit.toString(),
      if (search != null) 'search': search,
      if (breed != null) 'breed': breed,
      if (gender != null) 'gender': gender,
    };
    
    final query = queryParams.entries
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');
    
    final data = await get('/dogs?$query');
    return (data['dogs'] as List)
        .map((json) => Dog.fromJson(json))
        .toList();
  }

  Future<Dog> createDog(Map<String, dynamic> dogData) async {
    final data = await post('/dogs', dogData);
    return Dog.fromJson(data['dog']);
  }

  Future<Dog> updateDog(String dogId, Map<String, dynamic> dogData) async {
    final data = await put('/dogs/$dogId', dogData);
    return Dog.fromJson(data['dog']);
  }

  Future<void> deleteDog(String dogId) async {
    await delete('/dogs/$dogId');
  }

  // Litter methods
  Future<List<Map<String, dynamic>>> getLitters({
    int page = 1,
    int limit = 20,
    String? status,
    String? parent,
  }) async {
    // Temporarily disable to isolate the parsing issue
    print('🔍 ApiService.getLitters() called - returning empty list for now');
    return [];

    // final queryParams = <String, String>{
    //   'page': page.toString(),
    //   'limit': limit.toString(),
    //   if (status != null) 'status': status,
    //   if (parent != null) 'parent': parent,
    // };

    // final query = queryParams.entries
    //     .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
    //     .join('&');

    // final data = await get('/litters${query.isNotEmpty ? '?$query' : ''}');
    // return List<Map<String, dynamic>>.from(data);
  }

  Future<Map<String, dynamic>> createLitter(Map<String, dynamic> litterData) async {
    final data = await post('/litters', litterData);
    return data;
  }

  Future<Map<String, dynamic>> updateLitter(String litterId, Map<String, dynamic> litterData) async {
    final data = await put('/litters/$litterId', litterData);
    return data;
  }

  Future<void> deleteLitter(String litterId) async {
    await delete('/litters/$litterId');
  }

  Future<Map<String, dynamic>> getLitter(String litterId) async {
    return await get('/litters/$litterId');
  }

  // Message methods
  Future<List<Chat>> getChats({int page = 1, int limit = 20}) async {
    final data = await get('/messages/chats?page=$page&limit=$limit');
    return (data['chats'] as List)
        .map((json) => Chat.fromJson(json))
        .toList();
  }

  Future<List<Message>> getMessages(String chatId, {int page = 1, int limit = 50}) async {
    final data = await get('/messages/chats/$chatId/messages?page=$page&limit=$limit');
    return (data['messages'] as List)
        .map((json) => Message.fromJson(json))
        .toList();
  }

  Future<Message> sendMessage({
    required String receiverId,
    required String content,
    String messageType = 'text',
  }) async {
    final data = await post('/messages/send', {
      'receiver_id': receiverId,
      'content': content,
      'message_type': messageType,
    });
    return Message.fromJson(data['data']);
  }

  // Token management
  Future<void> _saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
  }

  Future<void> _removeToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
  }

  Future<String?> getSavedToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token');
    if (token != null) {
      setToken(token);
    }
    return token;
  }
}

// Exception class for API errors
class ApiException implements Exception {
  final String message;
  final int? statusCode;

  ApiException(this.message, {this.statusCode});

  @override
  String toString() => 'ApiException: $message';
}

// Response models
class AuthResponse {
  final String message;
  final User user;
  final String token;

  AuthResponse({
    required this.message,
    required this.user,
    required this.token,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      message: json['message'],
      user: User.fromJson(json['user']),
      token: json['token'],
    );
  }
}

// Simple model classes (you can expand these)
class User {
  final String id;
  final String email;
  final String displayName;
  final String? firstName;
  final String? lastName;
  final String? profilePhotoUrl;
  final bool isPremium;

  User({
    required this.id,
    required this.email,
    required this.displayName,
    this.firstName,
    this.lastName,
    this.profilePhotoUrl,
    required this.isPremium,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      email: json['email'],
      displayName: json['display_name'],
      firstName: json['first_name'],
      lastName: json['last_name'],
      profilePhotoUrl: json['profile_photo_url'],
      isPremium: json['is_premium'] ?? false,
    );
  }
}

class Dog {
  final String id;
  final String name;
  final String breed;
  final String gender;
  final String? birthDate;
  final double? weight;
  final String? color;
  final String ownerId;

  Dog({
    required this.id,
    required this.name,
    required this.breed,
    required this.gender,
    this.birthDate,
    this.weight,
    this.color,
    required this.ownerId,
  });

  factory Dog.fromJson(Map<String, dynamic> json) {
    return Dog(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      breed: json['breed'] ?? '',
      gender: json['gender'] ?? '',
      birthDate: json['birth_date'],
      weight: json['weight']?.toDouble(),
      color: json['color'],
      ownerId: json['owner_id'] ?? '',
    );
  }
}

class Chat {
  final String id;
  final String chatType;
  final User? otherParticipant;
  final Message? lastMessage;
  final int unreadCount;

  Chat({
    required this.id,
    required this.chatType,
    this.otherParticipant,
    this.lastMessage,
    required this.unreadCount,
  });

  factory Chat.fromJson(Map<String, dynamic> json) {
    return Chat(
      id: json['id'],
      chatType: json['chat_type'],
      otherParticipant: json['other_participant'] != null
          ? User.fromJson(json['other_participant'])
          : null,
      lastMessage: json['last_message'] != null
          ? Message.fromJson(json['last_message'])
          : null,
      unreadCount: json['unread_count'] ?? 0,
    );
  }
}

class Message {
  final String id;
  final String content;
  final String messageType;
  final String senderId;
  final String receiverId;
  final DateTime createdAt;
  final bool isRead;

  Message({
    required this.id,
    required this.content,
    required this.messageType,
    required this.senderId,
    required this.receiverId,
    required this.createdAt,
    required this.isRead,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'],
      content: json['content'],
      messageType: json['message_type'],
      senderId: json['sender_id'],
      receiverId: json['receiver_id'],
      createdAt: DateTime.parse(json['created_at']),
      isRead: json['is_read'] ?? false,
    );
  }
}

// Provider for API service
final apiServiceProvider = Provider<ApiService>((ref) {
  return ApiService();
});
