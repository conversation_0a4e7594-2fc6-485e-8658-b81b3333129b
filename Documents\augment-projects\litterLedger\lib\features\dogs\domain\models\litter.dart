enum LitterStatus { planned, pregnant, born, weaned, completed }

class Litter {
  final String id;
  final String name;
  final DateTime birthDate;
  final DateTime? expectedBirthDate;
  final DateTime? breedingDate;
  final LitterStatus status;
  final int totalPuppies;
  final int alivePuppies;
  final int malePuppies;
  final int femalePuppies;
  final String? notes;
  final List<String> photos;
  final List<String> tags;
  final bool isPublic;
  final String ownerId;
  final String sireId;
  final String damId;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Litter({
    required this.id,
    required this.name,
    required this.birthDate,
    this.expectedBirthDate,
    this.breedingDate,
    this.status = LitterStatus.planned,
    this.totalPuppies = 0,
    this.alivePuppies = 0,
    this.malePuppies = 0,
    this.femalePuppies = 0,
    this.notes,
    this.photos = const [],
    this.tags = const [],
    this.isPublic = false,
    required this.ownerId,
    required this.sireId,
    required this.damId,
    required this.createdAt,
    required this.updatedAt,
  });

  // Calculate age in days
  int get ageInDays {
    final now = DateTime.now();
    return now.difference(birthDate).inDays;
  }

  // Calculate age in weeks
  int get ageInWeeks {
    return (ageInDays / 7).floor();
  }

  // Get formatted age string
  String get ageString {
    final days = ageInDays;
    final weeks = ageInWeeks;
    
    if (days < 0) {
      return 'Due in ${(-days)} day${(-days) == 1 ? '' : 's'}';
    } else if (days == 0) {
      return 'Born today';
    } else if (weeks == 0) {
      return '$days day${days == 1 ? '' : 's'} old';
    } else if (weeks < 8) {
      return '$weeks week${weeks == 1 ? '' : 's'} old';
    } else {
      final months = (weeks / 4.33).floor();
      return '$months month${months == 1 ? '' : 's'} old';
    }
  }

  // Convert to JSON for API
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'birth_date': birthDate.toIso8601String().split('T')[0],
      'expected_birth_date': expectedBirthDate?.toIso8601String().split('T')[0],
      'breeding_date': breedingDate?.toIso8601String().split('T')[0],
      'status': status.name,
      'total_puppies': totalPuppies,
      'alive_puppies': alivePuppies,
      'male_puppies': malePuppies,
      'female_puppies': femalePuppies,
      'notes': notes,
      'photos': photos,
      'tags': tags,
      'is_public': isPublic,
      'owner_id': ownerId,
      'sire_id': sireId,
      'dam_id': damId,
    };
  }

  // Create from JSON (API response)
  factory Litter.fromJson(Map<String, dynamic> json) {
    return Litter(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      birthDate: DateTime.parse(json['birth_date']),
      expectedBirthDate: json['expected_birth_date'] != null
          ? DateTime.parse(json['expected_birth_date'])
          : null,
      breedingDate: json['breeding_date'] != null
          ? DateTime.parse(json['breeding_date'])
          : null,
      status: LitterStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => LitterStatus.planned,
      ),
      totalPuppies: json['total_puppies'] ?? 0,
      alivePuppies: json['alive_puppies'] ?? 0,
      malePuppies: json['male_puppies'] ?? 0,
      femalePuppies: json['female_puppies'] ?? 0,
      notes: json['notes'],
      photos: List<String>.from(json['photos'] ?? []),
      tags: List<String>.from(json['tags'] ?? []),
      isPublic: json['is_public'] ?? false,
      ownerId: json['owner_id'] ?? '',
      sireId: json['sire_id'] ?? '',
      damId: json['dam_id'] ?? '',
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  // Copy with method for updates
  Litter copyWith({
    String? name,
    DateTime? birthDate,
    DateTime? expectedBirthDate,
    DateTime? breedingDate,
    LitterStatus? status,
    int? totalPuppies,
    int? alivePuppies,
    int? malePuppies,
    int? femalePuppies,
    String? notes,
    List<String>? photos,
    List<String>? tags,
    bool? isPublic,
    String? sireId,
    String? damId,
  }) {
    return Litter(
      id: id,
      name: name ?? this.name,
      birthDate: birthDate ?? this.birthDate,
      expectedBirthDate: expectedBirthDate ?? this.expectedBirthDate,
      breedingDate: breedingDate ?? this.breedingDate,
      status: status ?? this.status,
      totalPuppies: totalPuppies ?? this.totalPuppies,
      alivePuppies: alivePuppies ?? this.alivePuppies,
      malePuppies: malePuppies ?? this.malePuppies,
      femalePuppies: femalePuppies ?? this.femalePuppies,
      notes: notes ?? this.notes,
      photos: photos ?? this.photos,
      tags: tags ?? this.tags,
      isPublic: isPublic ?? this.isPublic,
      ownerId: ownerId,
      sireId: sireId ?? this.sireId,
      damId: damId ?? this.damId,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Litter && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Litter(id: $id, name: $name, status: $status)';
}
