#!/bin/bash

# LitterLedger Release Build Script
# This script builds the app for both iOS and Android release

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="LitterLedger"
VERSION="1.0.0"
BUILD_NUMBER="1"

echo -e "${BLUE}🚀 Starting $APP_NAME Release Build${NC}"
echo -e "${BLUE}Version: $VERSION ($BUILD_NUMBER)${NC}"
echo ""

# Function to print section headers
print_section() {
    echo -e "${YELLOW}📦 $1${NC}"
    echo "----------------------------------------"
}

# Function to print success messages
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error messages
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    print_error "pubspec.yaml not found. Please run this script from the project root."
    exit 1
fi

print_section "Cleaning Previous Builds"
flutter clean
print_success "Clean completed"

print_section "Getting Dependencies"
flutter pub get
print_success "Dependencies updated"

print_section "Running Code Analysis"
flutter analyze
if [ $? -ne 0 ]; then
    print_error "Code analysis failed. Please fix issues before building."
    exit 1
fi
print_success "Code analysis passed"

print_section "Running Tests"
flutter test
if [ $? -ne 0 ]; then
    print_error "Tests failed. Please fix failing tests before building."
    exit 1
fi
print_success "All tests passed"

# Build for Android
print_section "Building Android Release"
echo "Building Android App Bundle..."
flutter build appbundle --release --build-name=$VERSION --build-number=$BUILD_NUMBER

if [ $? -eq 0 ]; then
    print_success "Android App Bundle built successfully"
    echo "📱 Location: build/app/outputs/bundle/release/app-release.aab"
else
    print_error "Android build failed"
    exit 1
fi

echo ""
echo "Building Android APK (for testing)..."
flutter build apk --release --build-name=$VERSION --build-number=$BUILD_NUMBER

if [ $? -eq 0 ]; then
    print_success "Android APK built successfully"
    echo "📱 Location: build/app/outputs/flutter-apk/app-release.apk"
else
    print_error "Android APK build failed"
fi

# Build for iOS (only on macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    print_section "Building iOS Release"
    
    # Check if iOS development tools are available
    if ! command -v xcodebuild &> /dev/null; then
        print_error "Xcode command line tools not found. Please install Xcode."
        exit 1
    fi
    
    echo "Building iOS Archive..."
    flutter build ios --release --build-name=$VERSION --build-number=$BUILD_NUMBER
    
    if [ $? -eq 0 ]; then
        print_success "iOS build completed successfully"
        echo "📱 Next step: Open ios/Runner.xcworkspace in Xcode to archive and upload"
    else
        print_error "iOS build failed"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️  iOS build skipped (not running on macOS)${NC}"
fi

# Generate build info
print_section "Generating Build Information"
cat > build_info.txt << EOF
$APP_NAME Build Information
==========================
Version: $VERSION
Build Number: $BUILD_NUMBER
Build Date: $(date)
Git Commit: $(git rev-parse HEAD 2>/dev/null || echo "Not a git repository")
Git Branch: $(git branch --show-current 2>/dev/null || echo "Not a git repository")

Android Outputs:
- App Bundle: build/app/outputs/bundle/release/app-release.aab
- APK: build/app/outputs/flutter-apk/app-release.apk

iOS Outputs:
- Archive via Xcode: ios/Runner.xcworkspace

Build Environment:
- Flutter Version: $(flutter --version | head -n 1)
- Dart Version: $(dart --version)
- OS: $(uname -s) $(uname -r)
EOF

print_success "Build information saved to build_info.txt"

# File size information
print_section "Build Artifacts Information"
if [ -f "build/app/outputs/bundle/release/app-release.aab" ]; then
    AAB_SIZE=$(du -h "build/app/outputs/bundle/release/app-release.aab" | cut -f1)
    echo "📦 Android App Bundle: $AAB_SIZE"
fi

if [ -f "build/app/outputs/flutter-apk/app-release.apk" ]; then
    APK_SIZE=$(du -h "build/app/outputs/flutter-apk/app-release.apk" | cut -f1)
    echo "📦 Android APK: $APK_SIZE"
fi

echo ""
print_section "Build Summary"
print_success "Release build completed successfully!"
echo ""
echo -e "${BLUE}Next Steps:${NC}"
echo "1. Test the release builds on physical devices"
echo "2. Upload Android App Bundle to Google Play Console"
echo "3. Archive and upload iOS build via Xcode"
echo "4. Submit for review on both app stores"
echo ""
echo -e "${YELLOW}Important Notes:${NC}"
echo "- Test in-app purchases in sandbox environment"
echo "- Verify Firebase configuration for production"
echo "- Check app signing certificates"
echo "- Review app store metadata and screenshots"
echo ""
echo -e "${GREEN}🎉 Happy launching!${NC}"
