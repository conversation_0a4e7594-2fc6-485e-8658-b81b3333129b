import 'package:cloud_firestore/cloud_firestore.dart';

enum Gender { male, female }

enum HealthStatus { excellent, good, fair, poor, unknown }

class Dog {
  final String id;
  final String name;
  final String breed;
  final Gender gender;
  final DateTime? birthDate;
  final String? photoUrl;
  final String? microchipId;
  final String? registrationNumber;
  final String ownerId;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Pedigree information
  final String? sireId;
  final String? damId;
  final String? sireName;
  final String? damName;
  
  // Physical characteristics
  final double? weight;
  final double? height;
  final String? color;
  final String? markings;
  
  // Health information
  final HealthStatus healthStatus;
  final List<String> healthRecords;
  final List<String> vaccinations;
  final String? veterinarian;
  final DateTime? lastVetVisit;
  
  // Genetic traits
  final Map<String, String> geneticTraits;
  final List<String> geneticTests;
  
  // Breeding information
  final bool isBreedingEligible;
  final List<String> offspring;
  final int litterCount;
  
  // Additional notes
  final String? notes;
  final List<String> tags;

  const Dog({
    required this.id,
    required this.name,
    required this.breed,
    required this.gender,
    this.birthDate,
    this.photoUrl,
    this.microchipId,
    this.registrationNumber,
    required this.ownerId,
    required this.createdAt,
    required this.updatedAt,
    this.sireId,
    this.damId,
    this.sireName,
    this.damName,
    this.weight,
    this.height,
    this.color,
    this.markings,
    this.healthStatus = HealthStatus.unknown,
    this.healthRecords = const [],
    this.vaccinations = const [],
    this.veterinarian,
    this.lastVetVisit,
    this.geneticTraits = const {},
    this.geneticTests = const [],
    this.isBreedingEligible = false,
    this.offspring = const [],
    this.litterCount = 0,
    this.notes,
    this.tags = const [],
  });

  // Calculate age in years
  int? get ageInYears {
    if (birthDate == null) return null;
    final now = DateTime.now();
    final age = now.difference(birthDate!).inDays / 365.25;
    return age.floor();
  }

  // Calculate age in months for puppies
  int? get ageInMonths {
    if (birthDate == null) return null;
    final now = DateTime.now();
    final age = now.difference(birthDate!).inDays / 30.44;
    return age.floor();
  }

  // Get formatted age string
  String get ageString {
    if (birthDate == null) return 'Unknown age';
    
    final years = ageInYears!;
    final months = ageInMonths!;
    
    if (years == 0) {
      return '$months month${months == 1 ? '' : 's'} old';
    } else if (years == 1 && months < 18) {
      final remainingMonths = months - 12;
      if (remainingMonths == 0) {
        return '1 year old';
      } else {
        return '1 year, $remainingMonths month${remainingMonths == 1 ? '' : 's'} old';
      }
    } else {
      return '$years year${years == 1 ? '' : 's'} old';
    }
  }

  // Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'breed': breed,
      'gender': gender.name,
      'birthDate': birthDate?.millisecondsSinceEpoch,
      'photoUrl': photoUrl,
      'microchipId': microchipId,
      'registrationNumber': registrationNumber,
      'ownerId': ownerId,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'sireId': sireId,
      'damId': damId,
      'sireName': sireName,
      'damName': damName,
      'weight': weight,
      'height': height,
      'color': color,
      'markings': markings,
      'healthStatus': healthStatus.name,
      'healthRecords': healthRecords,
      'vaccinations': vaccinations,
      'veterinarian': veterinarian,
      'lastVetVisit': lastVetVisit?.millisecondsSinceEpoch,
      'geneticTraits': geneticTraits,
      'geneticTests': geneticTests,
      'isBreedingEligible': isBreedingEligible,
      'offspring': offspring,
      'litterCount': litterCount,
      'notes': notes,
      'tags': tags,
    };
  }

  // Create from Firestore document
  factory Dog.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return Dog(
      id: doc.id,
      name: data['name'] ?? '',
      breed: data['breed'] ?? '',
      gender: Gender.values.firstWhere(
        (g) => g.name == data['gender'],
        orElse: () => Gender.male,
      ),
      birthDate: data['birthDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(data['birthDate'])
          : null,
      photoUrl: data['photoUrl'],
      microchipId: data['microchipId'],
      registrationNumber: data['registrationNumber'],
      ownerId: data['ownerId'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(data['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(data['updatedAt']),
      sireId: data['sireId'],
      damId: data['damId'],
      sireName: data['sireName'],
      damName: data['damName'],
      weight: data['weight']?.toDouble(),
      height: data['height']?.toDouble(),
      color: data['color'],
      markings: data['markings'],
      healthStatus: HealthStatus.values.firstWhere(
        (h) => h.name == data['healthStatus'],
        orElse: () => HealthStatus.unknown,
      ),
      healthRecords: List<String>.from(data['healthRecords'] ?? []),
      vaccinations: List<String>.from(data['vaccinations'] ?? []),
      veterinarian: data['veterinarian'],
      lastVetVisit: data['lastVetVisit'] != null
          ? DateTime.fromMillisecondsSinceEpoch(data['lastVetVisit'])
          : null,
      geneticTraits: Map<String, String>.from(data['geneticTraits'] ?? {}),
      geneticTests: List<String>.from(data['geneticTests'] ?? []),
      isBreedingEligible: data['isBreedingEligible'] ?? false,
      offspring: List<String>.from(data['offspring'] ?? []),
      litterCount: data['litterCount'] ?? 0,
      notes: data['notes'],
      tags: List<String>.from(data['tags'] ?? []),
    );
  }

  // Create from JSON (for offline storage)
  factory Dog.fromJson(Map<String, dynamic> json) {
    return Dog(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      breed: json['breed'] ?? '',
      gender: Gender.values.firstWhere(
        (g) => g.name == json['gender'],
        orElse: () => Gender.male,
      ),
      birthDate: json['birthDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['birthDate'])
          : null,
      photoUrl: json['photoUrl'],
      microchipId: json['microchipId'],
      registrationNumber: json['registrationNumber'],
      ownerId: json['ownerId'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(json['updatedAt']),
      sireId: json['sireId'],
      damId: json['damId'],
      sireName: json['sireName'],
      damName: json['damName'],
      weight: json['weight']?.toDouble(),
      height: json['height']?.toDouble(),
      color: json['color'],
      markings: json['markings'],
      healthStatus: HealthStatus.values.firstWhere(
        (h) => h.name == json['healthStatus'],
        orElse: () => HealthStatus.unknown,
      ),
      healthRecords: List<String>.from(json['healthRecords'] ?? []),
      vaccinations: List<String>.from(json['vaccinations'] ?? []),
      veterinarian: json['veterinarian'],
      lastVetVisit: json['lastVetVisit'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['lastVetVisit'])
          : null,
      geneticTraits: Map<String, String>.from(json['geneticTraits'] ?? {}),
      geneticTests: List<String>.from(json['geneticTests'] ?? []),
      isBreedingEligible: json['isBreedingEligible'] ?? false,
      offspring: List<String>.from(json['offspring'] ?? []),
      litterCount: json['litterCount'] ?? 0,
      notes: json['notes'],
      tags: List<String>.from(json['tags'] ?? []),
    );
  }

  // Copy with method for updates
  Dog copyWith({
    String? name,
    String? breed,
    Gender? gender,
    DateTime? birthDate,
    String? photoUrl,
    String? microchipId,
    String? registrationNumber,
    String? sireId,
    String? damId,
    String? sireName,
    String? damName,
    double? weight,
    double? height,
    String? color,
    String? markings,
    HealthStatus? healthStatus,
    List<String>? healthRecords,
    List<String>? vaccinations,
    String? veterinarian,
    DateTime? lastVetVisit,
    Map<String, String>? geneticTraits,
    List<String>? geneticTests,
    bool? isBreedingEligible,
    List<String>? offspring,
    int? litterCount,
    String? notes,
    List<String>? tags,
  }) {
    return Dog(
      id: id,
      name: name ?? this.name,
      breed: breed ?? this.breed,
      gender: gender ?? this.gender,
      birthDate: birthDate ?? this.birthDate,
      photoUrl: photoUrl ?? this.photoUrl,
      microchipId: microchipId ?? this.microchipId,
      registrationNumber: registrationNumber ?? this.registrationNumber,
      ownerId: ownerId,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      sireId: sireId ?? this.sireId,
      damId: damId ?? this.damId,
      sireName: sireName ?? this.sireName,
      damName: damName ?? this.damName,
      weight: weight ?? this.weight,
      height: height ?? this.height,
      color: color ?? this.color,
      markings: markings ?? this.markings,
      healthStatus: healthStatus ?? this.healthStatus,
      healthRecords: healthRecords ?? this.healthRecords,
      vaccinations: vaccinations ?? this.vaccinations,
      veterinarian: veterinarian ?? this.veterinarian,
      lastVetVisit: lastVetVisit ?? this.lastVetVisit,
      geneticTraits: geneticTraits ?? this.geneticTraits,
      geneticTests: geneticTests ?? this.geneticTests,
      isBreedingEligible: isBreedingEligible ?? this.isBreedingEligible,
      offspring: offspring ?? this.offspring,
      litterCount: litterCount ?? this.litterCount,
      notes: notes ?? this.notes,
      tags: tags ?? this.tags,
    );
  }
}
