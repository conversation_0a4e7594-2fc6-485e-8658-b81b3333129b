const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Chat = sequelize.define('Chat', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true // For group chats
    },
    chat_type: {
      type: DataTypes.ENUM('direct', 'group'),
      defaultValue: 'direct'
    },
    last_message_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    }
  }, {
    indexes: [
      {
        fields: ['chat_type']
      },
      {
        fields: ['last_message_at']
      },
      {
        fields: ['is_active']
      }
    ]
  });

  // Class methods
  Chat.findOrCreateDirectChat = async function(user1Id, user2Id) {
    // Look for existing direct chat between these users
    const existingChat = await Chat.findOne({
      where: {
        chat_type: 'direct'
      },
      include: [{
        model: sequelize.models.User,
        as: 'participants',
        where: {
          id: [user1Id, user2Id]
        },
        through: { attributes: [] }
      }]
    });

    if (existingChat && existingChat.participants.length === 2) {
      return existingChat;
    }

    // Create new chat
    const newChat = await Chat.create({
      chat_type: 'direct'
    });

    // Add participants
    await newChat.addParticipants([user1Id, user2Id]);

    return newChat;
  };

  return Chat;
};
