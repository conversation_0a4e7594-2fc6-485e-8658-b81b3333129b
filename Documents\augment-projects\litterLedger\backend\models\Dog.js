const { DataTypes, Op } = require('sequelize');

module.exports = (sequelize) => {
  const Dog = sequelize.define('Dog', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    breed: {
      type: DataTypes.STRING,
      allowNull: false
    },
    gender: {
      type: DataTypes.ENUM('male', 'female'),
      allowNull: false
    },
    birth_date: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    weight: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true
    },
    height: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true
    },
    color: {
      type: DataTypes.STRING,
      allowNull: true
    },
    markings: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    microchip_number: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true
    },
    registration_number: {
      type: DataTypes.STRING,
      allowNull: true
    },
    registration_body: {
      type: DataTypes.STRING,
      allowNull: true
    },
    health_status: {
      type: DataTypes.ENUM('healthy', 'sick', 'injured', 'recovering', 'unknown'),
      defaultValue: 'unknown'
    },
    is_breeding_eligible: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    is_retired: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    photos: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    genetic_traits: {
      type: DataTypes.JSON,
      defaultValue: {}
    },
    genetic_tests: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    health_clearances: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    vaccinations: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    temperament: {
      type: DataTypes.JSON,
      defaultValue: {}
    },
    achievements: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    tags: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    is_public: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    owner_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'User',
        key: 'id'
      }
    },
    sire_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'Dog',
        key: 'id'
      }
    },
    dam_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'Dog',
        key: 'id'
      }
    }
  }, {
    indexes: [
      {
        fields: ['owner_id']
      },
      {
        fields: ['breed']
      },
      {
        fields: ['gender']
      },
      {
        fields: ['is_breeding_eligible']
      },
      {
        fields: ['is_public']
      },
      {
        unique: true,
        fields: ['microchip_number'],
        where: {
          microchip_number: {
            [Op.ne]: null
          }
        }
      }
    ]
  });

  // Instance methods
  Dog.prototype.getAge = function() {
    if (!this.birth_date) return null;
    
    const today = new Date();
    const birthDate = new Date(this.birth_date);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  Dog.prototype.getAgeInMonths = function() {
    if (!this.birth_date) return null;
    
    const today = new Date();
    const birthDate = new Date(this.birth_date);
    
    let months = (today.getFullYear() - birthDate.getFullYear()) * 12;
    months -= birthDate.getMonth();
    months += today.getMonth();
    
    if (today.getDate() < birthDate.getDate()) {
      months--;
    }
    
    return months;
  };

  Dog.prototype.getPrimaryPhoto = function() {
    if (!this.photos || this.photos.length === 0) return null;
    return this.photos.find(photo => photo.is_primary) || this.photos[0];
  };

  Dog.prototype.isBreedingAge = function() {
    const ageInMonths = this.getAgeInMonths();
    if (!ageInMonths) return false;
    
    // Generally, dogs can breed after 18-24 months
    return ageInMonths >= 18;
  };

  return Dog;
};
