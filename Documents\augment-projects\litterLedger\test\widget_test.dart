// This is a basic Flutter widget test for LitterLedger.

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:litter_ledger/main.dart';

void main() {
  testWidgets('LitterLedger app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: LitterLedgerApp()));

    // Verify that the login screen is displayed
    expect(find.text('LitterLedger'), findsOneWidget);
    expect(find.text('Manage your dog breeding with ease'), findsOneWidget);
    expect(find.text('Sign In'), findsOneWidget);
  });
}
