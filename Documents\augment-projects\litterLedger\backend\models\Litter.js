const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Litter = sequelize.define('Litter', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    birth_date: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    expected_birth_date: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    breeding_date: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('planned', 'pregnant', 'born', 'weaned', 'completed'),
      defaultValue: 'planned'
    },
    total_puppies: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    alive_puppies: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    male_puppies: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    female_puppies: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    photos: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    tags: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    is_public: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    owner_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'User',
        key: 'id'
      }
    },
    sire_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Dog',
        key: 'id'
      }
    },
    dam_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Dog',
        key: 'id'
      }
    }
  }, {
    indexes: [
      {
        fields: ['owner_id']
      },
      {
        fields: ['sire_id']
      },
      {
        fields: ['dam_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['birth_date']
      },
      {
        fields: ['is_public']
      }
    ]
  });

  return Litter;
};
