const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Message = sequelize.define('Message', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    message_type: {
      type: DataTypes.ENUM('text', 'image', 'file'),
      defaultValue: 'text'
    },
    attachment_url: {
      type: DataTypes.STRING,
      allowNull: true
    },
    attachment_name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    attachment_size: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    is_read: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    read_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    sender_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'User',
        key: 'id'
      }
    },
    receiver_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'User',
        key: 'id'
      }
    },
    chat_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'Chat',
        key: 'id'
      }
    },
    reply_to_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'Message',
        key: 'id'
      }
    }
  }, {
    indexes: [
      {
        fields: ['sender_id']
      },
      {
        fields: ['receiver_id']
      },
      {
        fields: ['chat_id']
      },
      {
        fields: ['created_at']
      },
      {
        fields: ['is_read']
      }
    ]
  });

  // Instance methods
  Message.prototype.markAsRead = async function() {
    this.is_read = true;
    this.read_at = new Date();
    await this.save();
  };

  return Message;
};
