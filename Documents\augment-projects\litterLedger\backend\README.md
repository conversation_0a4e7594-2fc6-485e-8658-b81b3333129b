# LitterLedger Backend API

A self-hosted Node.js backend for the LitterLedger dog breeding management application.

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ 
- npm or yarn
- SQLite (included) or PostgreSQL (optional)

### Installation

1. **Navigate to backend directory**
```bash
cd backend
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Start the server**
```bash
# Development mode
npm run dev

# Production mode
npm start
```

The server will start on `http://localhost:3001`

## 📋 Environment Configuration

Copy `.env.example` to `.env` and configure:

```env
# Basic Configuration
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# Database (SQLite for development)
DB_STORAGE=./database.sqlite

# JWT Security
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production
JWT_EXPIRES_IN=7d

# File Uploads
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=********
```

## 🗄️ Database

The backend uses SQLite by default for easy setup. The database will be created automatically when you start the server.

### Database Schema

- **Users**: User accounts and profiles
- **Dogs**: Dog profiles and breeding information
- **Messages**: Real-time messaging between users
- **Chats**: Chat conversations
- **HealthRecords**: Health and veterinary records

## 🔌 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `POST /api/auth/google` - Google OAuth login
- `GET /api/auth/me` - Get current user
- `POST /api/auth/refresh` - Refresh JWT token
- `POST /api/auth/logout` - Logout user

### Users
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `GET /api/users/:id/public` - Get public user profile
- `GET /api/users/search` - Search users/breeders
- `PUT /api/users/preferences` - Update preferences
- `POST /api/users/upgrade-premium` - Upgrade to premium
- `DELETE /api/users/account` - Delete account

### Dogs
- `GET /api/dogs` - Get user's dogs
- `GET /api/dogs/public` - Get public dogs
- `GET /api/dogs/:id` - Get single dog
- `POST /api/dogs` - Create new dog
- `PUT /api/dogs/:id` - Update dog
- `DELETE /api/dogs/:id` - Delete dog
- `GET /api/dogs/:id/health` - Get dog's health records

### Messages (Premium Feature)
- `GET /api/messages/chats` - Get user's chats
- `GET /api/messages/chats/:chatId/messages` - Get messages in chat
- `POST /api/messages/send` - Send message
- `POST /api/messages/chats` - Start new chat
- `PUT /api/messages/chats/:chatId/read` - Mark messages as read
- `GET /api/messages/unread-count` - Get unread count

### File Uploads
- `POST /api/uploads/dog-photos` - Upload dog photos
- `POST /api/uploads/profile-photo` - Upload profile photo
- `POST /api/uploads/health-records` - Upload health record attachments
- `DELETE /api/uploads/:filename` - Delete uploaded file
- `GET /api/uploads/my-files` - Get user's files

## 🔒 Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Premium Features

Some features require premium access:
- Unlimited dog profiles (free users limited to 3)
- In-app messaging
- Advanced features

## 🌐 Real-time Features

The backend includes Socket.IO for real-time messaging:

### Socket Events
- `join` - Join user's personal room
- `send_message` - Send real-time message
- `new_message` - Receive new message
- `typing` - Typing indicators
- `user_typing` - Receive typing status

## 📁 File Uploads

Files are stored locally in the `uploads/` directory, organized by user ID:

```
uploads/
├── user-id-1/
│   ├── dog-photos/
│   ├── profile-photos/
│   └── health-records/
└── user-id-2/
    └── ...
```

### Supported File Types
- Images: JPEG, PNG, WebP
- Maximum size: 10MB per file
- Automatic image resizing and optimization

## 🚀 Deployment

### Docker Deployment

1. **Create Dockerfile**
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

2. **Build and run**
```bash
docker build -t litterledger-backend .
docker run -p 3001:3001 -v $(pwd)/uploads:/app/uploads litterledger-backend
```

### Traditional Server Deployment

1. **Install Node.js on server**
2. **Clone repository**
3. **Install dependencies**: `npm ci --only=production`
4. **Set environment variables**
5. **Use PM2 for process management**:
```bash
npm install -g pm2
pm2 start server.js --name litterledger-backend
pm2 startup
pm2 save
```

### Environment Variables for Production

```env
NODE_ENV=production
PORT=3001
JWT_SECRET=your_very_secure_secret_key_here
DB_STORAGE=/var/lib/litterledger/database.sqlite
UPLOAD_DIR=/var/lib/litterledger/uploads
```

## 🔧 Development

### Running in Development
```bash
npm run dev  # Uses nodemon for auto-restart
```

### Database Management
```bash
npm run migrate  # Run database migrations
npm run seed     # Seed with sample data
```

### Testing
```bash
npm test  # Run test suite
```

## 📊 Monitoring

### Health Check
```bash
curl http://localhost:3001/health
```

### Logs
The server logs all requests and errors. In production, consider using a logging service.

## 🔐 Security Features

- JWT authentication
- Rate limiting
- Input validation
- File type restrictions
- SQL injection prevention
- XSS protection
- CORS configuration
- Helmet security headers

## 🆘 Troubleshooting

### Common Issues

1. **Port already in use**
   - Change PORT in .env file
   - Kill existing process: `lsof -ti:3001 | xargs kill`

2. **Database connection errors**
   - Check DB_STORAGE path permissions
   - Ensure directory exists

3. **File upload errors**
   - Check UPLOAD_DIR permissions
   - Verify disk space

4. **JWT errors**
   - Ensure JWT_SECRET is set
   - Check token expiration

### Debug Mode
```bash
DEBUG=litterledger:* npm run dev
```

## 📚 API Documentation

For detailed API documentation with examples, see the Postman collection or use tools like Swagger/OpenAPI.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Add tests
5. Submit pull request

## 📄 License

MIT License - see LICENSE file for details.

---

**Self-hosted, secure, and scalable backend for LitterLedger!** 🐕
