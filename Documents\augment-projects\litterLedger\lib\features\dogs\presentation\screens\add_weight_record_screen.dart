import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/puppy.dart';
import '../../domain/models/weight_record.dart';

class AddWeightRecordScreen extends ConsumerStatefulWidget {
  final Puppy puppy;
  final WeightRecord? weightRecord;

  const AddWeightRecordScreen({
    super.key,
    required this.puppy,
    this.weightRecord,
  });

  @override
  ConsumerState<AddWeightRecordScreen> createState() => _AddWeightRecordScreenState();
}

class _AddWeightRecordScreenState extends ConsumerState<AddWeightRecordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _weightController = TextEditingController();
  final _notesController = TextEditingController();
  final _recordedByController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  bool get _isEditing => widget.weightRecord != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _initializeWithExistingRecord();
    }
  }

  void _initializeWithExistingRecord() {
    final record = widget.weightRecord!;
    _weightController.text = record.weight.toString();
    _notesController.text = record.notes ?? '';
    _recordedByController.text = record.recordedBy ?? '';
    _selectedDate = record.date;
  }

  @override
  void dispose() {
    _weightController.dispose();
    _notesController.dispose();
    _recordedByController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Weight Record' : 'Add Weight Record'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveWeightRecord,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildPuppyInfoCard(),
              const SizedBox(height: 24),
              _buildWeightInfoCard(),
              const SizedBox(height: 24),
              _buildNotesCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPuppyInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Puppy Information',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(
                  widget.puppy.gender == Gender.male ? Icons.male : Icons.female,
                  color: widget.puppy.gender == Gender.male ? Colors.blue : Colors.pink,
                ),
                const SizedBox(width: 8),
                Text(
                  widget.puppy.displayName,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            if (widget.puppy.currentWeight != null) ...[
              const SizedBox(height: 8),
              Text(
                'Current Weight: ${widget.puppy.currentWeight!.toStringAsFixed(2)} kg',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildWeightInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Weight Information',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextFormField(
                    controller: _weightController,
                    decoration: const InputDecoration(
                      labelText: 'Weight (kg) *',
                      border: OutlineInputBorder(),
                      suffixText: 'kg',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter a weight';
                      }
                      if (double.tryParse(value) == null) {
                        return 'Please enter a valid number';
                      }
                      final weight = double.parse(value);
                      if (weight <= 0) {
                        return 'Weight must be greater than 0';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 3,
                  child: _buildDateField(),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _recordedByController,
              decoration: const InputDecoration(
                labelText: 'Recorded By',
                hintText: 'Who recorded this weight?',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            _buildAgeCalculation(),
          ],
        ),
      ),
    );
  }

  Widget _buildDateField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Date *'),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(4),
          ),
          child: GestureDetector(
            onTap: _selectDate,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(_formatDate(_selectedDate)),
                const Icon(Icons.calendar_today),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAgeCalculation() {
    // Calculate age based on litter birth date (would need litter info)
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          const Icon(Icons.info, color: Colors.blue),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Age calculation will be automatically computed based on litter birth date',
              style: TextStyle(
                color: Colors.blue[700],
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notes',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Additional Notes',
                hintText: 'Any observations about the puppy at this weight...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  void _saveWeightRecord() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    // TODO: Implement save functionality
    Future.delayed(const Duration(seconds: 1), () {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_isEditing ? 'Weight record updated!' : 'Weight record added!'),
        ),
      );
      Navigator.of(context).pop();
    });
  }
}
