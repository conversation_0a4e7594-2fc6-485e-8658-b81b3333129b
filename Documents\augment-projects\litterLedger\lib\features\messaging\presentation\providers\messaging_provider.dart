import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';
import '../../domain/models/message.dart';
import '../../data/repositories/messaging_repository.dart';

// Repository provider
final messagingRepositoryProvider = Provider<MessagingRepository>((ref) {
  return MessagingRepository();
});

// User chats provider
final userChatsProvider = StreamProvider<List<Chat>>((ref) {
  final repository = ref.watch(messagingRepositoryProvider);
  return repository.getUserChats();
});

// Chat messages provider
final chatMessagesProvider = StreamProvider.family<List<Message>, String>((ref, chatId) {
  final repository = ref.watch(messagingRepositoryProvider);
  return repository.getChatMessages(chatId);
});

// Total unread count provider
final totalUnreadCountProvider = FutureProvider<int>((ref) {
  final repository = ref.watch(messagingRepositoryProvider);
  return repository.getTotalUnreadCount();
});

// Message sending state
class MessageSendingState {
  final bool isLoading;
  final String? error;

  const MessageSendingState({
    this.isLoading = false,
    this.error,
  });

  MessageSendingState copyWith({
    bool? isLoading,
    String? error,
  }) {
    return MessageSendingState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Message sending provider
final messageSendingProvider = StateNotifierProvider<MessageSendingNotifier, MessageSendingState>((ref) {
  return MessageSendingNotifier(ref.watch(messagingRepositoryProvider));
});

class MessageSendingNotifier extends StateNotifier<MessageSendingState> {
  MessageSendingNotifier(this._repository) : super(const MessageSendingState());

  final MessagingRepository _repository;

  // Send text message
  Future<bool> sendMessage(String chatId, String content) async {
    if (content.trim().isEmpty) return false;

    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.sendMessage(chatId, content.trim());
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  // Send image message
  Future<bool> sendImageMessage(String chatId, File imageFile, String? caption) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.sendImageMessage(chatId, imageFile, caption);
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Chat creation state
class ChatCreationState {
  final bool isLoading;
  final String? error;
  final String? chatId;

  const ChatCreationState({
    this.isLoading = false,
    this.error,
    this.chatId,
  });

  ChatCreationState copyWith({
    bool? isLoading,
    String? error,
    String? chatId,
  }) {
    return ChatCreationState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      chatId: chatId ?? this.chatId,
    );
  }
}

// Chat creation provider
final chatCreationProvider = StateNotifierProvider<ChatCreationNotifier, ChatCreationState>((ref) {
  return ChatCreationNotifier(ref.watch(messagingRepositoryProvider));
});

class ChatCreationNotifier extends StateNotifier<ChatCreationState> {
  ChatCreationNotifier(this._repository) : super(const ChatCreationState());

  final MessagingRepository _repository;

  // Get or create chat
  Future<String?> getOrCreateChat(String otherUserId, String otherUserName, String? otherUserPhoto) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final chatId = await _repository.getOrCreateChat(otherUserId, otherUserName, otherUserPhoto);
      state = state.copyWith(isLoading: false, chatId: chatId);
      return chatId;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return null;
    }
  }

  // Clear state
  void clear() {
    state = const ChatCreationState();
  }
}

// Message actions provider
final messageActionsProvider = StateNotifierProvider<MessageActionsNotifier, AsyncValue<void>>((ref) {
  return MessageActionsNotifier(ref.watch(messagingRepositoryProvider));
});

class MessageActionsNotifier extends StateNotifier<AsyncValue<void>> {
  MessageActionsNotifier(this._repository) : super(const AsyncValue.data(null));

  final MessagingRepository _repository;

  // Mark messages as read
  Future<void> markMessagesAsRead(String chatId) async {
    try {
      await _repository.markMessagesAsRead(chatId);
    } catch (e) {
      // Silently fail for read receipts
    }
  }

  // Delete message
  Future<void> deleteMessage(String messageId) async {
    state = const AsyncValue.loading();
    
    try {
      await _repository.deleteMessage(messageId);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

// Single chat provider
final singleChatProvider = FutureProvider.family<Chat?, String>((ref, chatId) {
  final repository = ref.watch(messagingRepositoryProvider);
  return repository.getChatById(chatId);
});
