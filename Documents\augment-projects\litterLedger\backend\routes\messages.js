const express = require('express');
const { body, validationResult } = require('express-validator');
const { Message, Chat, User } = require('../models');
const { authenticateToken, requirePremium } = require('../middleware/auth');
const { Op } = require('sequelize');

const router = express.Router();

// Get user's chats
router.get('/chats', [authenticateToken, requirePremium], async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    const chats = await Chat.findAndCountAll({
      include: [
        {
          model: User,
          as: 'participants',
          where: { id: req.user.userId },
          through: { attributes: [] }
        },
        {
          model: User,
          as: 'participants',
          attributes: ['id', 'display_name', 'profile_photo_url', 'kennel_name'],
          through: { attributes: [] }
        },
        {
          model: Message,
          as: 'messages',
          limit: 1,
          order: [['created_at', 'DESC']],
          include: [
            {
              model: User,
              as: 'sender',
              attributes: ['id', 'display_name']
            }
          ]
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['last_message_at', 'DESC']],
      distinct: true
    });

    // Process chats to get the other participant and unread count
    const processedChats = await Promise.all(
      chats.rows.map(async (chat) => {
        const otherParticipant = chat.participants.find(
          p => p.id !== req.user.userId
        );

        const unreadCount = await Message.count({
          where: {
            chat_id: chat.id,
            receiver_id: req.user.userId,
            is_read: false
          }
        });

        return {
          id: chat.id,
          chat_type: chat.chat_type,
          last_message_at: chat.last_message_at,
          other_participant: otherParticipant,
          last_message: chat.messages[0] || null,
          unread_count: unreadCount
        };
      })
    );

    res.json({
      chats: processedChats,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: chats.count,
        pages: Math.ceil(chats.count / limit)
      }
    });

  } catch (error) {
    console.error('Get chats error:', error);
    res.status(500).json({
      error: 'Failed to fetch chats'
    });
  }
});

// Get messages in a chat
router.get('/chats/:chatId/messages', [authenticateToken, requirePremium], async (req, res) => {
  try {
    const { chatId } = req.params;
    const { page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    // Verify user is participant in this chat
    const chat = await Chat.findOne({
      where: { id: chatId },
      include: [
        {
          model: User,
          as: 'participants',
          where: { id: req.user.userId },
          through: { attributes: [] }
        }
      ]
    });

    if (!chat) {
      return res.status(404).json({
        error: 'Chat not found or access denied'
      });
    }

    const messages = await Message.findAndCountAll({
      where: { chat_id: chatId },
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'display_name', 'profile_photo_url']
        },
        {
          model: User,
          as: 'receiver',
          attributes: ['id', 'display_name']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });

    // Mark messages as read
    await Message.update(
      { is_read: true, read_at: new Date() },
      {
        where: {
          chat_id: chatId,
          receiver_id: req.user.userId,
          is_read: false
        }
      }
    );

    res.json({
      messages: messages.rows.reverse(), // Reverse to show oldest first
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: messages.count,
        pages: Math.ceil(messages.count / limit)
      }
    });

  } catch (error) {
    console.error('Get messages error:', error);
    res.status(500).json({
      error: 'Failed to fetch messages'
    });
  }
});

// Send a message
router.post('/send', [
  authenticateToken,
  requirePremium,
  body('receiver_id').isUUID(),
  body('content').trim().isLength({ min: 1, max: 1000 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { receiver_id, content, message_type = 'text' } = req.body;

    // Check if receiver exists
    const receiver = await User.findByPk(receiver_id);
    if (!receiver || !receiver.is_active) {
      return res.status(404).json({
        error: 'Receiver not found'
      });
    }

    // Can't send message to yourself
    if (receiver_id === req.user.userId) {
      return res.status(400).json({
        error: 'Cannot send message to yourself'
      });
    }

    // Find or create chat
    const chat = await Chat.findOrCreateDirectChat(req.user.userId, receiver_id);

    // Create message
    const message = await Message.create({
      content,
      message_type,
      sender_id: req.user.userId,
      receiver_id,
      chat_id: chat.id
    });

    // Update chat's last message time
    await chat.update({ last_message_at: new Date() });

    // Fetch the created message with associations
    const createdMessage = await Message.findByPk(message.id, {
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'display_name', 'profile_photo_url']
        },
        {
          model: User,
          as: 'receiver',
          attributes: ['id', 'display_name']
        }
      ]
    });

    res.status(201).json({
      message: 'Message sent successfully',
      data: createdMessage
    });

  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({
      error: 'Failed to send message'
    });
  }
});

// Start a new chat
router.post('/chats', [
  authenticateToken,
  requirePremium,
  body('participant_id').isUUID()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { participant_id } = req.body;

    // Check if participant exists
    const participant = await User.findByPk(participant_id);
    if (!participant || !participant.is_active) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    // Can't chat with yourself
    if (participant_id === req.user.userId) {
      return res.status(400).json({
        error: 'Cannot start chat with yourself'
      });
    }

    // Find or create chat
    const chat = await Chat.findOrCreateDirectChat(req.user.userId, participant_id);

    // Get chat with participants
    const chatWithParticipants = await Chat.findByPk(chat.id, {
      include: [
        {
          model: User,
          as: 'participants',
          attributes: ['id', 'display_name', 'profile_photo_url', 'kennel_name'],
          through: { attributes: [] }
        }
      ]
    });

    res.json({
      message: 'Chat created successfully',
      chat: chatWithParticipants
    });

  } catch (error) {
    console.error('Create chat error:', error);
    res.status(500).json({
      error: 'Failed to create chat'
    });
  }
});

// Mark messages as read
router.put('/chats/:chatId/read', [authenticateToken, requirePremium], async (req, res) => {
  try {
    const { chatId } = req.params;

    // Verify user is participant in this chat
    const chat = await Chat.findOne({
      where: { id: chatId },
      include: [
        {
          model: User,
          as: 'participants',
          where: { id: req.user.userId },
          through: { attributes: [] }
        }
      ]
    });

    if (!chat) {
      return res.status(404).json({
        error: 'Chat not found or access denied'
      });
    }

    // Mark all unread messages as read
    const [updatedCount] = await Message.update(
      { is_read: true, read_at: new Date() },
      {
        where: {
          chat_id: chatId,
          receiver_id: req.user.userId,
          is_read: false
        }
      }
    );

    res.json({
      message: 'Messages marked as read',
      updated_count: updatedCount
    });

  } catch (error) {
    console.error('Mark as read error:', error);
    res.status(500).json({
      error: 'Failed to mark messages as read'
    });
  }
});

// Get unread message count
router.get('/unread-count', [authenticateToken, requirePremium], async (req, res) => {
  try {
    const unreadCount = await Message.count({
      where: {
        receiver_id: req.user.userId,
        is_read: false
      }
    });

    res.json({ unread_count: unreadCount });

  } catch (error) {
    console.error('Get unread count error:', error);
    res.status(500).json({
      error: 'Failed to get unread count'
    });
  }
});

module.exports = router;
