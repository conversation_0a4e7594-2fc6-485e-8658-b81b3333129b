import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/litter.dart';
import '../widgets/litter_card.dart';
import 'add_edit_litter_screen.dart';
import 'litter_detail_screen.dart';

class LittersListScreen extends ConsumerStatefulWidget {
  const LittersListScreen({super.key});

  @override
  ConsumerState<LittersListScreen> createState() => _LittersListScreenState();
}

class _LittersListScreenState extends ConsumerState<LittersListScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildLittersContent(context),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToAddLitter(),
        child: const Icon(Icons.add),
        tooltip: 'Add Litter',
      ),
    );
  }

  Widget _buildLittersContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Connected to backend notice
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.cloud_done,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Litter management ready - Connected to backend!',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Litters list placeholder
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.family_restroom,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No litters registered yet',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Add your first litter to get started',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[500],
                        ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () => _navigateToAddLitter(),
                    icon: const Icon(Icons.add),
                    label: const Text('Add Litter'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToAddLitter() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddEditLitterScreen(),
      ),
    );
  }

  void _navigateToLitterDetail(Litter litter) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => LitterDetailScreen(litter: litter),
      ),
    );
  }
}
