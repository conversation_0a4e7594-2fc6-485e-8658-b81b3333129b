import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../auth/presentation/providers/auth_provider.dart';
import 'dogs_list_screen.dart';
import 'litters_list_screen.dart';

class DogsScreen extends ConsumerWidget {
  const DogsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);
    final user = authState.user;

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Dogs & Litters'),
          bottom: const TabBar(
            tabs: [
              Tab(
                icon: Icon(Icons.pets),
                text: 'My Dogs',
              ),
              Tab(
                icon: Icon(Icons.family_restroom),
                text: 'My Litters',
              ),
            ],
          ),
        ),
        body: user?.isPremium == true
            ? const TabBarView(
                children: [
                  DogsListScreen(),
                  LittersListScreen(),
                ],
              )
            : _buildPremiumRequired(context),
      ),
    );
  }



  Widget _buildPremiumRequired(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.star_border,
              size: 64,
              color: Colors.amber[600],
            ),
            const SizedBox(height: 16),
            Text(
              'Premium Feature',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Upgrade to Premium to manage unlimited dogs',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                // Navigate to premium screen
                DefaultTabController.of(context)?.animateTo(2);
              },
              child: const Text('Upgrade Now'),
            ),
          ],
        ),
      ),
    );
  }


}
