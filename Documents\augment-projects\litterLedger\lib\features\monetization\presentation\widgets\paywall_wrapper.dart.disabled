import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/purchase_provider.dart';
import '../screens/paywall_screen.dart';

class PaywallWrapper extends ConsumerWidget {
  final Widget child;
  final bool showPaywallImmediately;
  final String? featureName;

  const PaywallWrapper({
    super.key,
    required this.child,
    this.showPaywallImmediately = false,
    this.featureName,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hasPremiumAccess = ref.watch(hasPremiumAccessProvider);
    final showPaywall = ref.watch(paywallStateProvider);

    // If user has premium access, show the child widget
    if (hasPremiumAccess) {
      return child;
    }

    // If we should show paywall immediately or user doesn't have access
    if (showPaywallImmediately || showPaywall) {
      return PaywallScreen(canDismiss: !showPaywallImmediately);
    }

    // Show limited version with upgrade prompt
    return _buildLimitedVersion(context, ref);
  }

  Widget _buildLimitedVersion(BuildContext context, WidgetRef ref) {
    return Stack(
      children: [
        // Show the child but with overlay
        child,
        
        // Overlay with upgrade prompt
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.8),
                ],
              ),
            ),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.lock,
                    color: Colors.white,
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    featureName != null
                        ? 'Unlock $featureName'
                        : 'Unlock Premium Features',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Get full access to all features with a one-time purchase',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const PaywallScreen(canDismiss: true),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'Upgrade Now',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// Helper widget for premium feature gates
class PremiumFeatureGate extends ConsumerWidget {
  final Widget child;
  final Widget? fallback;
  final String? featureName;
  final VoidCallback? onUpgradePressed;

  const PremiumFeatureGate({
    super.key,
    required this.child,
    this.fallback,
    this.featureName,
    this.onUpgradePressed,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hasPremiumAccess = ref.watch(hasPremiumAccessProvider);

    if (hasPremiumAccess) {
      return child;
    }

    return fallback ?? _buildUpgradePrompt(context);
  }

  Widget _buildUpgradePrompt(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey[50],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.lock,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 12),
          Text(
            featureName != null
                ? '$featureName requires Premium'
                : 'Premium Feature',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Upgrade to LitterLedger Premium to access this feature',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onUpgradePressed ??
                () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const PaywallScreen(canDismiss: true),
                    ),
                  );
                },
            child: const Text('Upgrade Now'),
          ),
        ],
      ),
    );
  }
}

// Helper widget for premium badges
class PremiumBadge extends StatelessWidget {
  final bool showBadge;

  const PremiumBadge({super.key, this.showBadge = true});

  @override
  Widget build(BuildContext context) {
    if (!showBadge) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.amber,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        'PRO',
        style: TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
