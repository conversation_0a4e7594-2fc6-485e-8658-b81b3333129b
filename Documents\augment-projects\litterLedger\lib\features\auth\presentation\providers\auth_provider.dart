import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/services/auth_service.dart';

// Simple User model for temporary use
class SimpleUser {
  final String id;
  final String email;
  final String displayName;
  final bool isPremium;
  final String? photoURL;

  SimpleUser({
    required this.id,
    required this.email,
    required this.displayName,
    required this.isPremium,
    this.photoURL,
  });

  factory SimpleUser.fromJson(Map<String, dynamic> json) {
    return SimpleUser(
      id: json['id'].toString(),
      email: json['email'] ?? '',
      displayName: json['display_name'] ?? json['displayName'] ?? '',
      isPremium: json['is_premium'] ?? json['isPremium'] ?? false,
      photoURL: json['photo_url'] ?? json['photoURL'] ?? json['profile_photo_url'],
    );
  }
}

// Simple auth state
class SimpleAuthState {
  final SimpleUser? user;
  final bool isLoading;
  final String? error;

  const SimpleAuthState({
    this.user,
    this.isLoading = false,
    this.error,
  });

  SimpleAuthState copyWith({
    SimpleUser? user,
    bool? isLoading,
    String? error,
  }) {
    return SimpleAuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Auth state provider (simplified)
final authProvider = StateProvider<SimpleUser?>((ref) => null);

// Auth notifier for managing auth state
final authNotifierProvider = StateNotifierProvider<SimpleAuthNotifier, SimpleAuthState>((ref) {
  return SimpleAuthNotifier();
});

class SimpleAuthNotifier extends StateNotifier<SimpleAuthState> {
  SimpleAuthNotifier() : super(const SimpleAuthState()) {
    _initializeAuth();
  }

  // Initialize authentication state
  Future<void> _initializeAuth() async {
    state = state.copyWith(isLoading: true);

    try {
      // Check if user is already logged in
      if (await AuthService.isLoggedIn()) {
        final userData = await AuthService.getStoredUser();
        if (userData != null) {
          final user = SimpleUser.fromJson(userData);
          state = state.copyWith(user: user, isLoading: false);
          return;
        }
      }

      state = state.copyWith(isLoading: false);
    } catch (e) {
      print('Error initializing auth: $e');
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  // Sign in with email and password
  Future<void> signInWithEmailAndPassword(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await AuthService.signInWithEmailPassword(email, password);

      if (result['success'] == true) {
        final user = SimpleUser.fromJson(result['user']);
        state = state.copyWith(user: user, isLoading: false);
      } else {
        throw Exception(result['error'] ?? 'Login failed');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  // Sign in with Google
  Future<void> signInWithGoogle() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await AuthService.signInWithGoogle();

      if (result['success'] == true) {
        final user = SimpleUser.fromJson(result['user']);
        state = state.copyWith(user: user, isLoading: false);
      } else {
        throw Exception(result['error'] ?? 'Google Sign-In failed');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  // Sign in with Apple
  Future<void> signInWithApple() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await AuthService.signInWithApple();

      if (result['success'] == true) {
        final user = SimpleUser.fromJson(result['user']);
        state = state.copyWith(user: user, isLoading: false);
      } else {
        throw Exception(result['error'] ?? 'Apple Sign-In failed');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  // Sign up with email and password
  Future<void> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String displayName,
    String? firstName,
    String? lastName,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // For now, use the email/password login since we don't have a separate signup endpoint
      final result = await AuthService.signInWithEmailPassword(email, password);

      if (result['success'] == true) {
        final user = SimpleUser.fromJson(result['user']);
        state = state.copyWith(user: user, isLoading: false);
      } else {
        throw Exception(result['error'] ?? 'Signup failed');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  // Update user state
  void updateUser(SimpleUser user) {
    state = state.copyWith(user: user, isLoading: false);
  }

  // Sign out
  Future<void> signOut() async {
    state = state.copyWith(isLoading: true);

    try {
      await AuthService.signOut();
      state = state.copyWith(user: null, isLoading: false);
    } catch (e) {
      print('Error signing out: $e');
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }
}
