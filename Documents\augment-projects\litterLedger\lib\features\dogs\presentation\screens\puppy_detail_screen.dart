import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/puppy.dart';
import '../../domain/models/weight_record.dart';
import '../widgets/weight_chart.dart';
import 'add_edit_puppy_screen.dart';
import 'add_weight_record_screen.dart';

class PuppyDetailScreen extends ConsumerStatefulWidget {
  final Puppy puppy;

  const PuppyDetailScreen({
    super.key,
    required this.puppy,
  });

  @override
  ConsumerState<PuppyDetailScreen> createState() => _PuppyDetailScreenState();
}

class _PuppyDetailScreenState extends ConsumerState<PuppyDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.puppy.displayName),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _navigateToEditPuppy(),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'delete':
                  _showDeleteDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete Puppy'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Weight'),
            Tab(text: 'Health'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildWeightTab(),
          _buildHealthTab(),
        ],
      ),
      floatingActionButton: _tabController.index == 1
          ? FloatingActionButton(
              onPressed: () => _navigateToAddWeight(),
              child: const Icon(Icons.add),
              tooltip: 'Add Weight Record',
            )
          : null,
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBasicInfoCard(),
          const SizedBox(height: 16),
          _buildPhysicalCard(),
          const SizedBox(height: 16),
          _buildStatusCard(),
          if (widget.puppy.notes != null && widget.puppy.notes!.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildNotesCard(),
          ],
        ],
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            if (widget.puppy.name != null)
              _buildInfoRow('Name', widget.puppy.name!),
            _buildInfoRow('Gender', widget.puppy.gender.name.toUpperCase()),
            if (widget.puppy.collarColor != null)
              _buildInfoRow('Collar Color', widget.puppy.collarColor!),
            if (widget.puppy.microchipNumber != null)
              _buildInfoRow('Microchip', widget.puppy.microchipNumber!),
            if (widget.puppy.registrationNumber != null)
              _buildInfoRow('Registration', widget.puppy.registrationNumber!),
          ],
        ),
      ),
    );
  }

  Widget _buildPhysicalCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Physical Characteristics',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            if (widget.puppy.birthWeight != null)
              _buildInfoRow('Birth Weight', '${widget.puppy.birthWeight!.toStringAsFixed(2)} kg'),
            if (widget.puppy.currentWeight != null)
              _buildInfoRow('Current Weight', '${widget.puppy.currentWeight!.toStringAsFixed(2)} kg'),
            if (widget.puppy.color != null)
              _buildInfoRow('Color', widget.puppy.color!),
            if (widget.puppy.markings != null)
              _buildInfoRow('Markings', widget.puppy.markings!),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Status',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatusChip(
                    'Status',
                    widget.puppy.status.name.toUpperCase(),
                    _getStatusColor(widget.puppy.status),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatusChip(
                    'Health',
                    widget.puppy.healthStatus.name.toUpperCase(),
                    _getHealthStatusColor(widget.puppy.healthStatus),
                  ),
                ),
              ],
            ),
            if (widget.puppy.salePrice != null) ...[
              const SizedBox(height: 16),
              _buildInfoRow('Sale Price', '\$${widget.puppy.salePrice!.toStringAsFixed(2)}'),
            ],
            if (widget.puppy.saleDate != null) ...[
              _buildInfoRow('Sale Date', _formatDate(widget.puppy.saleDate!)),
            ],
            if (widget.puppy.buyerName != null) ...[
              _buildInfoRow('Buyer', widget.puppy.buyerName!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notes',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(widget.puppy.notes!),
          ],
        ),
      ),
    );
  }

  Widget _buildWeightTab() {
    // TODO: Load actual weight records from API
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.monitor_weight, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('No weight records yet'),
          SizedBox(height: 8),
          Text('Tap the + button to add weight records'),
        ],
      ),
    );
  }

  Widget _buildHealthTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.health_and_safety, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('No health records yet'),
          SizedBox(height: 8),
          Text('Health records will appear here'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(PuppyStatus status) {
    switch (status) {
      case PuppyStatus.alive:
        return Colors.green;
      case PuppyStatus.deceased:
        return Colors.red;
      case PuppyStatus.sold:
        return Colors.blue;
      case PuppyStatus.kept:
        return Colors.purple;
      case PuppyStatus.reserved:
        return Colors.orange;
    }
  }

  Color _getHealthStatusColor(HealthStatus status) {
    switch (status) {
      case HealthStatus.healthy:
        return Colors.green;
      case HealthStatus.sick:
        return Colors.red;
      case HealthStatus.injured:
        return Colors.orange;
      case HealthStatus.recovering:
        return Colors.blue;
      case HealthStatus.unknown:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _navigateToEditPuppy() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddEditPuppyScreen(puppy: widget.puppy),
      ),
    );
  }

  void _navigateToAddWeight() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddWeightRecordScreen(puppy: widget.puppy),
      ),
    );
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Puppy'),
        content: Text('Are you sure you want to delete "${widget.puppy.displayName}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement delete functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Delete functionality coming soon')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
