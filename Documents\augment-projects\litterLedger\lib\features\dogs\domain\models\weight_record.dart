class WeightRecord {
  final String id;
  final double weight;
  final DateTime date;
  final int? ageInDays;
  final String? notes;
  final String? recordedBy;
  final String puppyId;
  final String ownerId;
  final DateTime createdAt;
  final DateTime updatedAt;

  const WeightRecord({
    required this.id,
    required this.weight,
    required this.date,
    this.ageInDays,
    this.notes,
    this.recordedBy,
    required this.puppyId,
    required this.ownerId,
    required this.createdAt,
    required this.updatedAt,
  });

  // Get formatted weight string
  String get weightString {
    if (weight < 1) {
      return '${(weight * 1000).round()}g';
    } else {
      return '${weight.toStringAsFixed(2)}kg';
    }
  }

  // Get formatted age string
  String get ageString {
    if (ageInDays == null) return 'Unknown age';
    
    final days = ageInDays!;
    if (days == 0) {
      return 'Birth';
    } else if (days < 7) {
      return '$days day${days == 1 ? '' : 's'} old';
    } else if (days < 30) {
      final weeks = (days / 7).floor();
      final remainingDays = days % 7;
      if (remainingDays == 0) {
        return '$weeks week${weeks == 1 ? '' : 's'} old';
      } else {
        return '$weeks week${weeks == 1 ? '' : 's'}, $remainingDays day${remainingDays == 1 ? '' : 's'} old';
      }
    } else {
      final weeks = (days / 7).floor();
      return '$weeks week${weeks == 1 ? '' : 's'} old';
    }
  }

  // Convert to JSON for API
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'weight': weight,
      'date': date.toIso8601String().split('T')[0],
      'age_in_days': ageInDays,
      'notes': notes,
      'recorded_by': recordedBy,
      'puppy_id': puppyId,
      'owner_id': ownerId,
    };
  }

  // Create from JSON (API response)
  factory WeightRecord.fromJson(Map<String, dynamic> json) {
    return WeightRecord(
      id: json['id'] ?? '',
      weight: (json['weight'] ?? 0.0).toDouble(),
      date: DateTime.parse(json['date']),
      ageInDays: json['age_in_days'],
      notes: json['notes'],
      recordedBy: json['recorded_by'],
      puppyId: json['puppy_id'] ?? '',
      ownerId: json['owner_id'] ?? '',
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  // Copy with method for updates
  WeightRecord copyWith({
    double? weight,
    DateTime? date,
    int? ageInDays,
    String? notes,
    String? recordedBy,
  }) {
    return WeightRecord(
      id: id,
      weight: weight ?? this.weight,
      date: date ?? this.date,
      ageInDays: ageInDays ?? this.ageInDays,
      notes: notes ?? this.notes,
      recordedBy: recordedBy ?? this.recordedBy,
      puppyId: puppyId,
      ownerId: ownerId,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WeightRecord && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'WeightRecord(id: $id, weight: $weightString, date: $date)';
}
