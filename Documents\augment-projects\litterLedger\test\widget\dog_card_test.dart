import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:litterledger/features/dogs/domain/models/dog.dart';
import 'package:litterledger/features/dogs/presentation/widgets/dog_card.dart';

void main() {
  group('DogCard Widget Tests', () {
    late Dog testDog;

    setUp(() {
      testDog = Dog(
        id: 'test-id',
        name: '<PERSON>',
        breed: 'Golden Retriever',
        gender: Gender.male,
        birthDate: DateTime.now().subtract(const Duration(days: 365 * 2)),
        ownerId: 'owner-123',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        weight: 25.5,
        color: 'Golden',
        healthStatus: HealthStatus.healthy,
      );
    });

    Widget createTestWidget(Dog dog, {VoidCallback? onTap}) {
      return ProviderScope(
        child: MaterialApp(
          home: Scaffold(
            body: Dog<PERSON><PERSON>(
              dog: dog,
              onTap: onTap,
            ),
          ),
        ),
      );
    }

    testWidgets('should display dog name', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget(testDog));

      // Assert
      expect(find.text('Buddy'), findsOneWidget);
    });

    testWidgets('should display dog breed', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget(testDog));

      // Assert
      expect(find.text('Golden Retriever'), findsOneWidget);
    });

    testWidgets('should display dog gender', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget(testDog));

      // Assert
      expect(find.text('Male'), findsOneWidget);
    });

    testWidgets('should display female gender correctly', (WidgetTester tester) async {
      // Arrange
      final femaleDog = testDog.copyWith(gender: Gender.female);

      // Act
      await tester.pumpWidget(createTestWidget(femaleDog));

      // Assert
      expect(find.text('Female'), findsOneWidget);
    });

    testWidgets('should display dog age when birthDate is provided', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget(testDog));

      // Assert
      expect(find.textContaining('2 years'), findsOneWidget);
    });

    testWidgets('should handle missing birthDate gracefully', (WidgetTester tester) async {
      // Arrange
      final dogWithoutBirthDate = testDog.copyWith(birthDate: null);

      // Act
      await tester.pumpWidget(createTestWidget(dogWithoutBirthDate));

      // Assert
      expect(find.text('Age unknown'), findsOneWidget);
    });

    testWidgets('should display health status', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget(testDog));

      // Assert
      expect(find.text('Healthy'), findsOneWidget);
    });

    testWidgets('should display different health statuses correctly', (WidgetTester tester) async {
      // Test sick status
      final sickDog = testDog.copyWith(healthStatus: HealthStatus.sick);
      await tester.pumpWidget(createTestWidget(sickDog));
      expect(find.text('Sick'), findsOneWidget);

      // Test injured status
      final injuredDog = testDog.copyWith(healthStatus: HealthStatus.injured);
      await tester.pumpWidget(createTestWidget(injuredDog));
      await tester.pumpAndSettle();
      expect(find.text('Injured'), findsOneWidget);
    });

    testWidgets('should call onTap when card is tapped', (WidgetTester tester) async {
      // Arrange
      bool wasTapped = false;
      void onTap() {
        wasTapped = true;
      }

      await tester.pumpWidget(createTestWidget(testDog, onTap: onTap));

      // Act
      await tester.tap(find.byType(Card));
      await tester.pumpAndSettle();

      // Assert
      expect(wasTapped, isTrue);
    });

    testWidgets('should display placeholder when no photo URL', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget(testDog));

      // Assert
      expect(find.byIcon(Icons.pets), findsOneWidget);
    });

    testWidgets('should display weight when provided', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget(testDog));

      // Assert
      expect(find.textContaining('25.5 kg'), findsOneWidget);
    });

    testWidgets('should handle missing weight gracefully', (WidgetTester tester) async {
      // Arrange
      final dogWithoutWeight = testDog.copyWith(weight: null);

      // Act
      await tester.pumpWidget(createTestWidget(dogWithoutWeight));

      // Assert
      expect(find.textContaining('kg'), findsNothing);
    });

    testWidgets('should display breeding eligibility status', (WidgetTester tester) async {
      // Arrange
      final breedingEligibleDog = testDog.copyWith(isBreedingEligible: true);

      // Act
      await tester.pumpWidget(createTestWidget(breedingEligibleDog));

      // Assert
      expect(find.byIcon(Icons.favorite), findsOneWidget);
    });

    testWidgets('should not display breeding icon when not eligible', (WidgetTester tester) async {
      // Arrange
      final nonBreedingDog = testDog.copyWith(isBreedingEligible: false);

      // Act
      await tester.pumpWidget(createTestWidget(nonBreedingDog));

      // Assert
      expect(find.byIcon(Icons.favorite), findsNothing);
    });

    testWidgets('should have proper card styling', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget(testDog));

      // Assert
      final cardFinder = find.byType(Card);
      expect(cardFinder, findsOneWidget);

      final card = tester.widget<Card>(cardFinder);
      expect(card.elevation, greaterThan(0));
    });

    testWidgets('should display all information in correct layout', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget(testDog));

      // Assert - Check that all expected elements are present
      expect(find.text('Buddy'), findsOneWidget);
      expect(find.text('Golden Retriever'), findsOneWidget);
      expect(find.text('Male'), findsOneWidget);
      expect(find.text('Healthy'), findsOneWidget);
      expect(find.textContaining('years'), findsOneWidget);
      expect(find.textContaining('kg'), findsOneWidget);
    });

    testWidgets('should be accessible', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget(testDog));

      // Assert - Check for semantic labels
      expect(find.bySemanticsLabel('Dog profile card for Buddy'), findsOneWidget);
    });
  });
}
