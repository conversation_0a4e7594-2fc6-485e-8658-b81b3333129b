# LitterLedger Testing Guide

## Overview

This guide covers the comprehensive testing strategy for LitterLedger, including unit tests, widget tests, integration tests, and manual testing procedures.

## 🧪 Test Types

### 1. Unit Tests
**Location**: `test/unit/`
**Purpose**: Test individual functions, methods, and classes in isolation

```bash
# Run unit tests
flutter test test/unit/

# Run with coverage
flutter test --coverage
```

**Coverage Areas**:
- Dog model serialization/deserialization
- Business logic validation
- Data transformation functions
- Utility functions
- Provider state management

### 2. Widget Tests
**Location**: `test/widget/`
**Purpose**: Test individual widgets and their interactions

```bash
# Run widget tests
flutter test test/widget/

# Run specific widget test
flutter test test/widget/dog_card_test.dart
```

**Coverage Areas**:
- UI component rendering
- User interaction handling
- Widget state changes
- Accessibility features
- Responsive design

### 3. Integration Tests
**Location**: `integration_test/`
**Purpose**: Test complete user workflows and app behavior

```bash
# Run integration tests
flutter test integration_test/

# Run on specific device
flutter test integration_test/ -d <device_id>
```

**Coverage Areas**:
- End-to-end user journeys
- Cross-screen navigation
- Data persistence
- Network connectivity
- Performance benchmarks

## 🎯 Testing Strategy

### Test Pyramid
```
    /\
   /  \    Integration Tests (Few)
  /____\   
 /      \   Widget Tests (Some)
/________\  
Unit Tests (Many)
```

### Coverage Goals
- **Unit Tests**: 80%+ code coverage
- **Widget Tests**: All critical UI components
- **Integration Tests**: All major user flows

## 🔧 Test Setup

### Dependencies
Add to `pubspec.yaml`:
```yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  mockito: ^5.4.4
  build_runner: ^2.4.7
  fake_cloud_firestore: ^2.4.1+1
  firebase_auth_mocks: ^0.13.0
```

### Test Configuration
Create `test/test_config.dart`:
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

// Mock classes and test utilities
class MockFirebaseAuth extends Mock implements FirebaseAuth {}
class MockFirestore extends Mock implements FirebaseFirestore {}

// Test data factories
Dog createTestDog({String? id, String? name}) {
  return Dog(
    id: id ?? 'test-id',
    name: name ?? 'Test Dog',
    breed: 'Test Breed',
    gender: Gender.male,
    ownerId: 'test-owner',
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
}
```

## 📋 Test Checklists

### Unit Test Checklist
- [ ] Model serialization/deserialization
- [ ] Business logic validation
- [ ] Edge cases and error handling
- [ ] Null safety compliance
- [ ] Data transformation accuracy
- [ ] Provider state management
- [ ] Repository methods
- [ ] Utility functions

### Widget Test Checklist
- [ ] Widget renders correctly
- [ ] User interactions work
- [ ] State changes properly
- [ ] Accessibility labels present
- [ ] Error states handled
- [ ] Loading states shown
- [ ] Responsive design works
- [ ] Theme switching works

### Integration Test Checklist
- [ ] App startup flow
- [ ] User authentication
- [ ] Dog profile management
- [ ] Navigation between screens
- [ ] Data persistence
- [ ] Offline functionality
- [ ] Network error handling
- [ ] Performance benchmarks

## 🚀 Running Tests

### Local Development
```bash
# Run all tests
flutter test

# Run with coverage
flutter test --coverage

# Generate coverage report
genhtml coverage/lcov.info -o coverage/html

# Run integration tests
flutter test integration_test/
```

### CI/CD Pipeline
```yaml
# GitHub Actions example
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - run: flutter pub get
      - run: flutter analyze
      - run: flutter test --coverage
      - run: flutter test integration_test/
```

## 📊 Test Data Management

### Test Fixtures
Create `test/fixtures/` for test data:
```dart
// test/fixtures/dog_fixtures.dart
final testDogs = [
  Dog(
    id: 'dog-1',
    name: 'Buddy',
    breed: 'Golden Retriever',
    // ... other properties
  ),
  // More test dogs
];
```

### Mock Data
Use consistent mock data across tests:
```dart
// test/mocks/mock_data.dart
class MockData {
  static const String testUserId = 'test-user-123';
  static const String testDogId = 'test-dog-456';
  
  static Dog get sampleDog => Dog(
    id: testDogId,
    name: 'Sample Dog',
    // ... other properties
  );
}
```

## 🔍 Manual Testing

### Device Testing Matrix
| Device Type | Screen Size | OS Version | Test Priority |
|-------------|-------------|------------|---------------|
| iPhone SE   | 4.7"        | iOS 15+    | High          |
| iPhone 14   | 6.1"        | iOS 16+    | High          |
| iPhone 14 Pro Max | 6.7"  | iOS 16+    | Medium        |
| iPad        | 10.9"       | iOS 15+    | Medium        |
| Pixel 6     | 6.4"        | Android 12+ | High         |
| Galaxy S22  | 6.1"        | Android 12+ | High         |
| Tablet      | 10"         | Android 11+ | Medium       |

### Test Scenarios

#### Authentication Flow
1. **Sign In**
   - [ ] Google Sign-In works
   - [ ] User profile created
   - [ ] Navigation to home screen
   - [ ] Error handling for failed sign-in

2. **Sign Out**
   - [ ] User can sign out
   - [ ] Data cleared appropriately
   - [ ] Navigation to login screen

#### Dog Management
1. **Create Dog Profile**
   - [ ] All required fields validated
   - [ ] Photo upload works
   - [ ] Data saves correctly
   - [ ] Navigation after creation

2. **Edit Dog Profile**
   - [ ] Existing data loads
   - [ ] Changes save correctly
   - [ ] Photo replacement works
   - [ ] Validation on updates

3. **Delete Dog Profile**
   - [ ] Confirmation dialog shown
   - [ ] Data deleted from database
   - [ ] UI updates correctly

#### Messaging System
1. **Send Message**
   - [ ] Message sends successfully
   - [ ] Real-time updates work
   - [ ] Message appears in chat
   - [ ] Timestamp accurate

2. **Receive Message**
   - [ ] Messages appear in real-time
   - [ ] Notifications work
   - [ ] Unread count updates
   - [ ] Chat list updates

#### Premium Features
1. **Purchase Flow**
   - [ ] Payment screen loads
   - [ ] Purchase completes
   - [ ] Features unlock
   - [ ] Receipt validation

2. **Feature Access**
   - [ ] Premium features accessible
   - [ ] Free users see paywall
   - [ ] Restore purchases works

### Performance Testing
1. **App Startup**
   - [ ] Cold start < 3 seconds
   - [ ] Warm start < 1 second
   - [ ] No ANRs or crashes

2. **Memory Usage**
   - [ ] Memory usage reasonable
   - [ ] No memory leaks
   - [ ] Handles low memory

3. **Network Performance**
   - [ ] Efficient data usage
   - [ ] Handles poor connectivity
   - [ ] Offline mode works

### Accessibility Testing
1. **Screen Reader**
   - [ ] All elements have labels
   - [ ] Navigation works with TalkBack/VoiceOver
   - [ ] Content reads logically

2. **Visual Accessibility**
   - [ ] Sufficient color contrast
   - [ ] Text scales properly
   - [ ] Touch targets adequate size

## 📈 Test Metrics

### Key Performance Indicators
- **Test Coverage**: Target 80%+
- **Test Execution Time**: < 5 minutes for full suite
- **Flaky Test Rate**: < 5%
- **Bug Detection Rate**: Track bugs found by tests vs. production

### Reporting
Generate test reports:
```bash
# Coverage report
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html

# Test results
flutter test --reporter json > test_results.json
```

## 🐛 Debugging Tests

### Common Issues
1. **Flaky Tests**
   - Add proper waits (`pumpAndSettle()`)
   - Use deterministic test data
   - Avoid time-dependent assertions

2. **Widget Test Failures**
   - Check widget tree structure
   - Verify finder selectors
   - Ensure proper test setup

3. **Integration Test Issues**
   - Verify device connectivity
   - Check app permissions
   - Ensure test environment setup

### Debug Tools
```bash
# Run tests in debug mode
flutter test --debug

# Verbose output
flutter test --verbose

# Run single test
flutter test test/unit/dog_model_test.dart
```

## 🔄 Continuous Testing

### Pre-commit Hooks
```bash
# Install pre-commit hooks
dart pub global activate pre_commit
pre_commit install

# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: flutter-test
        name: Flutter Test
        entry: flutter test
        language: system
        pass_filenames: false
```

### Test Automation
- Run tests on every commit
- Block merges if tests fail
- Generate coverage reports
- Notify team of test failures

---

**Remember**: Good tests are fast, reliable, and maintainable. Write tests that give you confidence in your code!
