import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart' as iap;
import '../../domain/models/purchase.dart';
import '../../data/repositories/purchase_repository.dart';

// Repository provider
final purchaseRepositoryProvider = Provider<PurchaseRepository>((ref) {
  final repository = PurchaseRepository();
  ref.onDispose(() => repository.dispose());
  return repository;
});

// User subscription provider
final userSubscriptionProvider = StreamProvider<UserSubscription>((ref) {
  final repository = ref.watch(purchaseRepositoryProvider);
  return repository.getUserSubscriptionStream();
});

// Available products provider
final availableProductsProvider = FutureProvider<List<iap.ProductDetails>>((ref) {
  final repository = ref.watch(purchaseRepositoryProvider);
  return repository.getAvailableProducts();
});

// Purchase history provider
final purchaseHistoryProvider = FutureProvider<List<Purchase>>((ref) {
  final repository = ref.watch(purchaseRepositoryProvider);
  return repository.getUserPurchases();
});

// Purchase state
class PurchaseState {
  final bool isLoading;
  final String? error;
  final bool isPurchasing;
  final bool isRestoring;

  const PurchaseState({
    this.isLoading = false,
    this.error,
    this.isPurchasing = false,
    this.isRestoring = false,
  });

  PurchaseState copyWith({
    bool? isLoading,
    String? error,
    bool? isPurchasing,
    bool? isRestoring,
  }) {
    return PurchaseState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isPurchasing: isPurchasing ?? this.isPurchasing,
      isRestoring: isRestoring ?? this.isRestoring,
    );
  }
}

// Purchase provider
final purchaseProvider = StateNotifierProvider<PurchaseNotifier, PurchaseState>((ref) {
  return PurchaseNotifier(ref.watch(purchaseRepositoryProvider));
});

class PurchaseNotifier extends StateNotifier<PurchaseState> {
  PurchaseNotifier(this._repository) : super(const PurchaseState()) {
    _initialize();
  }

  final PurchaseRepository _repository;

  // Initialize in-app purchases
  Future<void> _initialize() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.initialize();
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  // Purchase a product
  Future<bool> purchaseProduct(iap.ProductDetails product) async {
    state = state.copyWith(isPurchasing: true, error: null);
    
    try {
      final success = await _repository.purchaseProduct(product);
      state = state.copyWith(isPurchasing: false);
      return success;
    } catch (e) {
      state = state.copyWith(isPurchasing: false, error: e.toString());
      return false;
    }
  }

  // Restore purchases
  Future<bool> restorePurchases() async {
    state = state.copyWith(isRestoring: true, error: null);
    
    try {
      await _repository.restorePurchases();
      state = state.copyWith(isRestoring: false);
      return true;
    } catch (e) {
      state = state.copyWith(isRestoring: false, error: e.toString());
      return false;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Paywall state provider
final paywallStateProvider = Provider<bool>((ref) {
  final subscription = ref.watch(userSubscriptionProvider);
  
  return subscription.when(
    data: (sub) => !sub.isActive,
    loading: () => true, // Show paywall while loading
    error: (_, __) => true, // Show paywall on error
  );
});

// Premium features check provider
final hasPremiumAccessProvider = Provider<bool>((ref) {
  final subscription = ref.watch(userSubscriptionProvider);
  
  return subscription.when(
    data: (sub) => sub.isActive,
    loading: () => false,
    error: (_, __) => false,
  );
});
