import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:io';
import '../../domain/models/message.dart';

class MessagingRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Get current user ID
  String? get _currentUserId => _auth.currentUser?.uid;
  String get _currentUserName => _auth.currentUser?.displayName ?? _auth.currentUser?.email ?? 'Unknown';

  // Collection references
  CollectionReference get _chatsCollection => _firestore.collection('chats');
  CollectionReference get _messagesCollection => _firestore.collection('messages');

  // Get user's chats
  Stream<List<Chat>> getUserChats() {
    if (_currentUserId == null) {
      return Stream.value([]);
    }

    return _chatsCollection
        .where('participantIds', arrayContains: _currentUserId)
        .where('isActive', isEqualTo: true)
        .orderBy('updatedAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => Chat.fromFirestore(doc)).toList());
  }

  // Get or create chat between two users
  Future<String> getOrCreateChat(String otherUserId, String otherUserName, String? otherUserPhoto) async {
    if (_currentUserId == null) {
      throw Exception('User not authenticated');
    }

    try {
      // Check if chat already exists
      final existingChats = await _chatsCollection
          .where('participantIds', arrayContains: _currentUserId)
          .get();

      for (final doc in existingChats.docs) {
        final chat = Chat.fromFirestore(doc);
        if (chat.participantIds.contains(otherUserId) && chat.participantIds.length == 2) {
          return chat.id;
        }
      }

      // Create new chat
      final now = DateTime.now();
      final chatData = Chat(
        id: '',
        participantIds: [_currentUserId!, otherUserId],
        participantNames: {
          _currentUserId!: _currentUserName,
          otherUserId: otherUserName,
        },
        participantPhotos: {
          _currentUserId!: _auth.currentUser?.photoURL,
          otherUserId: otherUserPhoto,
        },
        unreadCounts: {
          _currentUserId!: 0,
          otherUserId: 0,
        },
        createdAt: now,
        updatedAt: now,
      );

      final docRef = await _chatsCollection.add(chatData.toFirestore());
      return docRef.id;
    } catch (e) {
      throw Exception('Failed to get or create chat: $e');
    }
  }

  // Get messages for a chat
  Stream<List<Message>> getChatMessages(String chatId) {
    return _messagesCollection
        .where('chatId', isEqualTo: chatId)
        .orderBy('timestamp', descending: true)
        .limit(50)
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => Message.fromFirestore(doc)).toList());
  }

  // Send a text message
  Future<void> sendMessage(String chatId, String content) async {
    if (_currentUserId == null) {
      throw Exception('User not authenticated');
    }

    try {
      final now = DateTime.now();
      final message = Message(
        id: '',
        chatId: chatId,
        senderId: _currentUserId!,
        senderName: _currentUserName,
        senderPhotoUrl: _auth.currentUser?.photoURL,
        content: content,
        type: MessageType.text,
        timestamp: now,
      );

      // Add message
      await _messagesCollection.add(message.toFirestore());

      // Update chat with last message info
      await _updateChatLastMessage(chatId, content, now);
    } catch (e) {
      throw Exception('Failed to send message: $e');
    }
  }

  // Send an image message
  Future<void> sendImageMessage(String chatId, File imageFile, String? caption) async {
    if (_currentUserId == null) {
      throw Exception('User not authenticated');
    }

    try {
      // Upload image
      final imageUrl = await _uploadMessageImage(chatId, imageFile);
      
      final now = DateTime.now();
      final message = Message(
        id: '',
        chatId: chatId,
        senderId: _currentUserId!,
        senderName: _currentUserName,
        senderPhotoUrl: _auth.currentUser?.photoURL,
        content: caption ?? 'Image',
        type: MessageType.image,
        timestamp: now,
        imageUrl: imageUrl,
      );

      // Add message
      await _messagesCollection.add(message.toFirestore());

      // Update chat with last message info
      await _updateChatLastMessage(chatId, 'Image', now);
    } catch (e) {
      throw Exception('Failed to send image: $e');
    }
  }

  // Upload message image
  Future<String> _uploadMessageImage(String chatId, File imageFile) async {
    try {
      final fileName = 'messages/$chatId/${DateTime.now().millisecondsSinceEpoch}.jpg';
      final ref = _storage.ref().child(fileName);
      
      final uploadTask = ref.putFile(imageFile);
      final snapshot = await uploadTask;
      
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw Exception('Failed to upload image: $e');
    }
  }

  // Update chat with last message info
  Future<void> _updateChatLastMessage(String chatId, String lastMessage, DateTime timestamp) async {
    try {
      final chatDoc = await _chatsCollection.doc(chatId).get();
      if (!chatDoc.exists) return;

      final chat = Chat.fromFirestore(chatDoc);
      final updatedUnreadCounts = Map<String, int>.from(chat.unreadCounts);

      // Increment unread count for other participants
      for (final participantId in chat.participantIds) {
        if (participantId != _currentUserId) {
          updatedUnreadCounts[participantId] = (updatedUnreadCounts[participantId] ?? 0) + 1;
        }
      }

      await _chatsCollection.doc(chatId).update({
        'lastMessage': lastMessage,
        'lastMessageTime': timestamp.millisecondsSinceEpoch,
        'lastMessageSenderId': _currentUserId,
        'unreadCounts': updatedUnreadCounts,
        'updatedAt': timestamp.millisecondsSinceEpoch,
      });
    } catch (e) {
      throw Exception('Failed to update chat: $e');
    }
  }

  // Mark messages as read
  Future<void> markMessagesAsRead(String chatId) async {
    if (_currentUserId == null) return;

    try {
      // Reset unread count for current user
      await _chatsCollection.doc(chatId).update({
        'unreadCounts.$_currentUserId': 0,
      });

      // Mark unread messages as read
      final unreadMessages = await _messagesCollection
          .where('chatId', isEqualTo: chatId)
          .where('senderId', isNotEqualTo: _currentUserId)
          .where('isRead', isEqualTo: false)
          .get();

      final batch = _firestore.batch();
      for (final doc in unreadMessages.docs) {
        batch.update(doc.reference, {'isRead': true});
      }
      await batch.commit();
    } catch (e) {
      throw Exception('Failed to mark messages as read: $e');
    }
  }

  // Delete a message
  Future<void> deleteMessage(String messageId) async {
    if (_currentUserId == null) {
      throw Exception('User not authenticated');
    }

    try {
      final messageDoc = await _messagesCollection.doc(messageId).get();
      if (!messageDoc.exists) {
        throw Exception('Message not found');
      }

      final message = Message.fromFirestore(messageDoc);
      if (message.senderId != _currentUserId) {
        throw Exception('Not authorized to delete this message');
      }

      // Delete image if it exists
      if (message.imageUrl != null && message.imageUrl!.isNotEmpty) {
        try {
          await _storage.refFromURL(message.imageUrl!).delete();
        } catch (e) {
          // Image deletion failed, but continue with message deletion
        }
      }

      await _messagesCollection.doc(messageId).delete();
    } catch (e) {
      throw Exception('Failed to delete message: $e');
    }
  }

  // Get chat by ID
  Future<Chat?> getChatById(String chatId) async {
    try {
      final doc = await _chatsCollection.doc(chatId).get();
      if (doc.exists) {
        return Chat.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get chat: $e');
    }
  }

  // Search for users to start a chat with
  Future<List<Map<String, dynamic>>> searchUsers(String query) async {
    try {
      // This is a simplified search - in a real app, you'd have a users collection
      // For now, we'll return an empty list as this would require user management
      return [];
    } catch (e) {
      throw Exception('Failed to search users: $e');
    }
  }

  // Get total unread message count
  Future<int> getTotalUnreadCount() async {
    if (_currentUserId == null) return 0;

    try {
      final chats = await _chatsCollection
          .where('participantIds', arrayContains: _currentUserId)
          .where('isActive', isEqualTo: true)
          .get();

      int totalUnread = 0;
      for (final doc in chats.docs) {
        final chat = Chat.fromFirestore(doc);
        totalUnread += chat.getUnreadCount(_currentUserId!);
      }

      return totalUnread;
    } catch (e) {
      return 0;
    }
  }
}
