const express = require('express');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const { User } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { Op } = require('sequelize');

const router = express.Router();

// Register new user
router.post('/register', [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 6 }),
  body('display_name').trim().isLength({ min: 1 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { email, password, display_name, first_name, last_name } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return res.status(409).json({
        error: 'User already exists',
        message: 'A user with this email already exists'
      });
    }

    // Create new user
    const user = await User.create({
      email,
      password,
      display_name,
      first_name,
      last_name,
      email_verified: false
    });

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    res.status(201).json({
      message: 'User created successfully',
      user: user.toJSON(),
      token
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      error: 'Registration failed',
      message: 'An error occurred during registration'
    });
  }
});

// Login user
router.post('/login', [
  body('email').isEmail().normalizeEmail(),
  body('password').exists()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { email, password } = req.body;

    // Find user
    const user = await User.findOne({ where: { email, is_active: true } });
    if (!user) {
      return res.status(401).json({
        error: 'Authentication failed',
        message: 'Invalid email or password'
      });
    }

    // Validate password
    const isValidPassword = await user.validatePassword(password);
    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Authentication failed',
        message: 'Invalid email or password'
      });
    }

    // Update last login
    user.last_login_at = new Date();
    await user.save();

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    res.json({
      message: 'Login successful',
      user: user.toJSON(),
      token
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      error: 'Login failed',
      message: 'An error occurred during login'
    });
  }
});

// Social authentication endpoint (Google, Apple, etc.)
router.post('/social', async (req, res) => {
  try {
    const { provider, token, email, display_name, photo_url } = req.body;

    // Validate required fields
    if (!provider || !token || !email || !display_name) {
      return res.status(400).json({
        error: 'Provider, token, email, and display name are required'
      });
    }

    console.log(`🔐 Social login attempt: ${provider} for ${email}`);

    // Check if user already exists
    let user = await User.findOne({ where: { email } });

    if (!user) {
      // Create new user for social login
      user = await User.create({
        email,
        password_hash: null, // No password for social login
        display_name,
        first_name: display_name.split(' ')[0] || display_name,
        last_name: display_name.split(' ').slice(1).join(' ') || null,
        is_premium: false,
        profile_photo_url: photo_url,
        email_verified: true,
        last_login_at: new Date()
      });
      console.log(`✅ Created new social user: ${email}`);
    } else {
      // Update existing user with social info if needed
      user.last_login_at = new Date();
      if (photo_url && !user.profile_photo_url) {
        user.profile_photo_url = photo_url;
      }
      await user.save();
      console.log(`✅ Social login for existing user: ${email}`);
    }

    // Generate JWT token
    const authToken = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        provider: provider
      },
      process.env.JWT_SECRET,
      { expiresIn: '30d' } // Longer expiry for social logins
    );

    res.json({
      message: 'Social login successful',
      user: user.toJSON(),
      token: authToken
    });

  } catch (error) {
    console.error('Social authentication error:', error);
    res.status(500).json({ error: 'Social authentication failed' });
  }
});

// Google OAuth login (simplified - you'd integrate with passport-google-oauth20)
router.post('/google', [
  body('google_token').exists()
], async (req, res) => {
  try {
    const { google_token, user_info } = req.body;

    // In a real implementation, you'd verify the Google token
    // For demo purposes, we'll trust the user_info

    const { email, name, picture, google_id } = user_info;

    // Find or create user
    let user = await User.findOne({
      where: {
        [Op.or]: [
          { email },
          { google_id }
        ]
      }
    });

    if (!user) {
      user = await User.create({
        email,
        display_name: name,
        profile_photo_url: picture,
        google_id,
        email_verified: true,
        password: null // OAuth users don't have passwords
      });
    } else {
      // Update user info
      user.display_name = name;
      user.profile_photo_url = picture;
      user.google_id = google_id;
      user.last_login_at = new Date();
      await user.save();
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    res.json({
      message: 'Google login successful',
      user: user.toJSON(),
      token
    });

  } catch (error) {
    console.error('Google login error:', error);
    res.status(500).json({
      error: 'Google login failed',
      message: 'An error occurred during Google login'
    });
  }
});

// Get current user
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const user = await User.findByPk(req.user.userId);
    if (!user) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    res.json({
      user: user.toJSON()
    });

  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      error: 'Failed to get user information'
    });
  }
});

// Refresh token
router.post('/refresh', authenticateToken, async (req, res) => {
  try {
    const user = await User.findByPk(req.user.userId);
    if (!user) {
      return res.status(404).json({
        error: 'User not found'
      });
    }

    // Generate new token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    res.json({
      message: 'Token refreshed',
      token
    });

  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(500).json({
      error: 'Failed to refresh token'
    });
  }
});

// Logout (client-side token removal, but we can blacklist tokens if needed)
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    // In a more sophisticated setup, you'd add the token to a blacklist
    res.json({
      message: 'Logout successful'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      error: 'Logout failed'
    });
  }
});

module.exports = router;
