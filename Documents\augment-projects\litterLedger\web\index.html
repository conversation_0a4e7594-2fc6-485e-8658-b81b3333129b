<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="LitterLedger - Dog breeding management app">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="LitterLedger">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>LitterLedger</title>
  <link rel="manifest" href="manifest.json">

  <!-- Google Sign-In -->
  <script src="https://accounts.google.com/gsi/client" async defer></script>

  <!-- Apple Sign-In -->
  <script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"></script>
</head>
<body>
  <!-- Google OAuth Configuration -->
  <script>
    window.googleOAuthConfig = {
      client_id: 'YOUR_GOOGLE_CLIENT_ID.apps.googleusercontent.com',
      scope: 'email profile',
      callback: 'handleGoogleSignIn'
    };
  </script>

  <!-- Apple OAuth Configuration -->
  <script>
    window.appleOAuthConfig = {
      clientId: 'com.yourcompany.litterledger.signin',
      scope: 'name email',
      redirectURI: window.location.origin + '/auth/apple/callback',
      state: 'apple_signin_state'
    };
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
