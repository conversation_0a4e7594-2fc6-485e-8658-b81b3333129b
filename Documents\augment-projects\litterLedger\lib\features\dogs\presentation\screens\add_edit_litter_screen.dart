import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/litter.dart';
import '../../domain/models/puppy.dart';

class AddEditLitterScreen extends ConsumerStatefulWidget {
  final Litter? litter;

  const AddEditLitterScreen({
    super.key,
    this.litter,
  });

  @override
  ConsumerState<AddEditLitterScreen> createState() => _AddEditLitterScreenState();
}

class _AddEditLitterScreenState extends ConsumerState<AddEditLitterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime? _birthDate;
  DateTime? _expectedBirthDate;
  DateTime? _breedingDate;
  LitterStatus _status = LitterStatus.planned;
  String? _selectedSireId;
  String? _selectedDamId;
  bool _isLoading = false;

  bool get _isEditing => widget.litter != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _initializeWithExistingLitter();
    }
  }

  void _initializeWithExistingLitter() {
    final litter = widget.litter!;
    _nameController.text = litter.name;
    _notesController.text = litter.notes ?? '';
    _birthDate = litter.birthDate;
    _expectedBirthDate = litter.expectedBirthDate;
    _breedingDate = litter.breedingDate;
    _status = litter.status;
    _selectedSireId = litter.sireId;
    _selectedDamId = litter.damId;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Litter' : 'Add Litter'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveLitter,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfoSection(),
              const SizedBox(height: 24),
              _buildParentsSection(),
              const SizedBox(height: 24),
              _buildDatesSection(),
              const SizedBox(height: 24),
              _buildStatusSection(),
              const SizedBox(height: 24),
              _buildNotesSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Litter Name *',
                hintText: 'e.g., Spring 2024 Litter',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a litter name';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildParentsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Parents',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildParentSelector(
                    'Sire (Father) *',
                    _selectedSireId,
                    (value) => setState(() => _selectedSireId = value),
                    Gender.male,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildParentSelector(
                    'Dam (Mother) *',
                    _selectedDamId,
                    (value) => setState(() => _selectedDamId = value),
                    Gender.female,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildParentSelector(
    String label,
    String? selectedId,
    Function(String?) onChanged,
    Gender gender,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(4),
          ),
          child: GestureDetector(
            onTap: () => _showDogSelector(gender, onChanged),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  selectedId != null ? 'Dog Selected' : 'Select ${gender.name}',
                  style: TextStyle(
                    color: selectedId != null ? null : Colors.grey[600],
                  ),
                ),
                const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDatesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Important Dates',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            _buildDateField(
              'Birth Date *',
              _birthDate,
              (date) => setState(() => _birthDate = date),
            ),
            const SizedBox(height: 16),
            _buildDateField(
              'Expected Birth Date',
              _expectedBirthDate,
              (date) => setState(() => _expectedBirthDate = date),
            ),
            const SizedBox(height: 16),
            _buildDateField(
              'Breeding Date',
              _breedingDate,
              (date) => setState(() => _breedingDate = date),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateField(
    String label,
    DateTime? date,
    Function(DateTime?) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(4),
          ),
          child: GestureDetector(
            onTap: () => _selectDate(onChanged),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  date != null ? _formatDate(date) : 'Select date',
                  style: TextStyle(
                    color: date != null ? null : Colors.grey[600],
                  ),
                ),
                const Icon(Icons.calendar_today),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Status',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<LitterStatus>(
              value: _status,
              decoration: const InputDecoration(
                labelText: 'Litter Status',
                border: OutlineInputBorder(),
              ),
              items: LitterStatus.values.map((status) {
                return DropdownMenuItem(
                  value: status,
                  child: Text(status.name.toUpperCase()),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() => _status = value);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notes',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Additional Notes',
                hintText: 'Any additional information about this litter...',
                border: OutlineInputBorder(),
              ),
              maxLines: 4,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _selectDate(Function(DateTime?) onChanged) async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (date != null) {
      onChanged(date);
    }
  }

  void _showDogSelector(Gender gender, Function(String?) onChanged) {
    // TODO: Implement dog selector dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Select ${gender.name}'),
        content: const Text('Dog selection functionality coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _saveLitter() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedSireId == null || _selectedDamId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select both sire and dam')),
      );
      return;
    }

    if (_birthDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a birth date')),
      );
      return;
    }

    setState(() => _isLoading = true);

    // TODO: Implement save functionality
    Future.delayed(const Duration(seconds: 1), () {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_isEditing ? 'Litter updated!' : 'Litter created!'),
        ),
      );
      Navigator.of(context).pop();
    });
  }
}
