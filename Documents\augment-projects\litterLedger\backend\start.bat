@echo off
echo 🚀 Starting LitterLedger Backend Server...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if package.json exists
if not exist package.json (
    echo ❌ package.json not found
    echo Please run this script from the backend directory
    pause
    exit /b 1
)

REM Check if node_modules exists
if not exist node_modules (
    echo 📦 Installing dependencies...
    npm install
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully
    echo.
)

REM Check if .env exists
if not exist .env (
    echo 📝 Setting up environment...
    node setup.js
    if errorlevel 1 (
        echo ❌ Setup failed
        pause
        exit /b 1
    )
    echo.
)

REM Start the server
echo 🌟 Starting server in development mode...
echo.
echo Server will be available at: http://localhost:3001
echo Health check: http://localhost:3001/health
echo.
echo Press Ctrl+C to stop the server
echo.

npm run dev

pause
