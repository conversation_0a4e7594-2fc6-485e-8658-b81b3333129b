import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../domain/models/dog.dart';
import '../providers/dog_provider.dart';

class AddEditDogScreen extends ConsumerStatefulWidget {
  final String? dogId;

  const AddEditDogScreen({super.key, this.dogId});

  @override
  ConsumerState<AddEditDogScreen> createState() => _AddEditDogScreenState();
}

class _AddEditDogScreenState extends ConsumerState<AddEditDogScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _breedController = TextEditingController();
  final _microchipController = TextEditingController();
  final _registrationController = TextEditingController();
  final _weightController = TextEditingController();
  final _heightController = TextEditingController();
  final _colorController = TextEditingController();
  final _markingsController = TextEditingController();
  final _notesController = TextEditingController();
  
  Gender _selectedGender = Gender.male;
  DateTime? _birthDate;
  HealthStatus _healthStatus = HealthStatus.unknown;
  bool _isBreedingEligible = false;
  File? _selectedImage;
  String? _currentPhotoUrl;
  
  bool get _isEditing => widget.dogId != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(dogFormProvider.notifier).loadDog(widget.dogId!);
      });
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _breedController.dispose();
    _microchipController.dispose();
    _registrationController.dispose();
    _weightController.dispose();
    _heightController.dispose();
    _colorController.dispose();
    _markingsController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final formState = ref.watch(dogFormProvider);
    
    // Load existing dog data when editing
    ref.listen(dogFormProvider, (previous, next) {
      if (next.dog != null && _isEditing) {
        _populateFormWithDogData(next.dog!);
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Dog' : 'Add Dog'),
        actions: [
          TextButton(
            onPressed: formState.isLoading ? null : _saveDog,
            child: formState.isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: formState.isLoading && _isEditing
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Photo section
                    _buildPhotoSection(),
                    const SizedBox(height: 24),
                    
                    // Basic information
                    _buildBasicInfoSection(),
                    const SizedBox(height: 24),
                    
                    // Physical characteristics
                    _buildPhysicalSection(),
                    const SizedBox(height: 24),
                    
                    // Health information
                    _buildHealthSection(),
                    const SizedBox(height: 24),
                    
                    // Additional notes
                    _buildNotesSection(),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildPhotoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Photo',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 12),
        Center(
          child: GestureDetector(
            onTap: _pickImage,
            child: Container(
              width: 150,
              height: 150,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
                color: Colors.grey[50],
              ),
              child: _selectedImage != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.file(
                        _selectedImage!,
                        fit: BoxFit.cover,
                      ),
                    )
                  : _currentPhotoUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.network(
                            _currentPhotoUrl!,
                            fit: BoxFit.cover,
                          ),
                        )
                      : Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.add_a_photo,
                              size: 48,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Add Photo',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Basic Information',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        
        // Name field
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: 'Name *',
            prefixIcon: Icon(Icons.pets),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter a name';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        
        // Breed field
        TextFormField(
          controller: _breedController,
          decoration: const InputDecoration(
            labelText: 'Breed *',
            prefixIcon: Icon(Icons.category),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter a breed';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        
        // Gender selection
        Row(
          children: [
            const Icon(Icons.wc, color: Colors.grey),
            const SizedBox(width: 12),
            const Text('Gender:'),
            const SizedBox(width: 16),
            Expanded(
              child: Row(
                children: [
                  Radio<Gender>(
                    value: Gender.male,
                    groupValue: _selectedGender,
                    onChanged: (value) {
                      setState(() {
                        _selectedGender = value!;
                      });
                    },
                  ),
                  const Text('Male'),
                  Radio<Gender>(
                    value: Gender.female,
                    groupValue: _selectedGender,
                    onChanged: (value) {
                      setState(() {
                        _selectedGender = value!;
                      });
                    },
                  ),
                  const Text('Female'),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        // Birth date
        ListTile(
          contentPadding: EdgeInsets.zero,
          leading: const Icon(Icons.cake),
          title: Text(_birthDate == null 
              ? 'Birth Date' 
              : 'Birth Date: ${_birthDate!.day}/${_birthDate!.month}/${_birthDate!.year}'),
          trailing: const Icon(Icons.calendar_today),
          onTap: _selectBirthDate,
        ),
      ],
    );
  }

  Widget _buildPhysicalSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Physical Characteristics',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _weightController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Weight (kg)',
                  prefixIcon: Icon(Icons.monitor_weight),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _heightController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Height (cm)',
                  prefixIcon: Icon(Icons.height),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        TextFormField(
          controller: _colorController,
          decoration: const InputDecoration(
            labelText: 'Color',
            prefixIcon: Icon(Icons.palette),
          ),
        ),
        const SizedBox(height: 16),
        
        TextFormField(
          controller: _markingsController,
          decoration: const InputDecoration(
            labelText: 'Markings',
            prefixIcon: Icon(Icons.brush),
          ),
          maxLines: 2,
        ),
      ],
    );
  }

  Widget _buildHealthSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Health Information',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        
        // Health status dropdown
        DropdownButtonFormField<HealthStatus>(
          value: _healthStatus,
          decoration: const InputDecoration(
            labelText: 'Health Status',
            prefixIcon: Icon(Icons.health_and_safety),
          ),
          items: HealthStatus.values.map((status) {
            return DropdownMenuItem(
              value: status,
              child: Text(status.name.toUpperCase()),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _healthStatus = value!;
            });
          },
        ),
        const SizedBox(height: 16),
        
        // Breeding eligible switch
        SwitchListTile(
          contentPadding: EdgeInsets.zero,
          title: const Text('Breeding Eligible'),
          subtitle: const Text('Is this dog eligible for breeding?'),
          value: _isBreedingEligible,
          onChanged: (value) {
            setState(() {
              _isBreedingEligible = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Additional Notes',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'Notes',
            prefixIcon: Icon(Icons.note),
            alignLabelWithHint: true,
          ),
          maxLines: 4,
        ),
      ],
    );
  }

  Future<void> _pickImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 1024,
      maxHeight: 1024,
      imageQuality: 85,
    );
    
    if (image != null) {
      setState(() {
        _selectedImage = File(image.path);
      });
    }
  }

  Future<void> _selectBirthDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _birthDate ?? DateTime.now().subtract(const Duration(days: 365)),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    
    if (picked != null) {
      setState(() {
        _birthDate = picked;
      });
    }
  }

  void _populateFormWithDogData(Dog dog) {
    _nameController.text = dog.name;
    _breedController.text = dog.breed;
    _microchipController.text = dog.microchipId ?? '';
    _registrationController.text = dog.registrationNumber ?? '';
    _weightController.text = dog.weight?.toString() ?? '';
    _heightController.text = dog.height?.toString() ?? '';
    _colorController.text = dog.color ?? '';
    _markingsController.text = dog.markings ?? '';
    _notesController.text = dog.notes ?? '';
    
    setState(() {
      _selectedGender = dog.gender;
      _birthDate = dog.birthDate;
      _healthStatus = dog.healthStatus;
      _isBreedingEligible = dog.isBreedingEligible;
      _currentPhotoUrl = dog.photoUrl;
    });
  }

  Future<void> _saveDog() async {
    if (!_formKey.currentState!.validate()) return;

    final now = DateTime.now();
    final dog = Dog(
      id: widget.dogId ?? '',
      name: _nameController.text.trim(),
      breed: _breedController.text.trim(),
      gender: _selectedGender,
      birthDate: _birthDate,
      photoUrl: _currentPhotoUrl,
      microchipId: _microchipController.text.trim().isEmpty 
          ? null 
          : _microchipController.text.trim(),
      registrationNumber: _registrationController.text.trim().isEmpty 
          ? null 
          : _registrationController.text.trim(),
      ownerId: '', // Will be set by repository
      createdAt: _isEditing ? ref.read(dogFormProvider).dog!.createdAt : now,
      updatedAt: now,
      weight: _weightController.text.trim().isEmpty 
          ? null 
          : double.tryParse(_weightController.text.trim()),
      height: _heightController.text.trim().isEmpty 
          ? null 
          : double.tryParse(_heightController.text.trim()),
      color: _colorController.text.trim().isEmpty 
          ? null 
          : _colorController.text.trim(),
      markings: _markingsController.text.trim().isEmpty 
          ? null 
          : _markingsController.text.trim(),
      healthStatus: _healthStatus,
      isBreedingEligible: _isBreedingEligible,
      notes: _notesController.text.trim().isEmpty 
          ? null 
          : _notesController.text.trim(),
    );

    bool success;
    if (_isEditing) {
      success = await ref.read(dogFormProvider.notifier).updateDog(dog);
    } else {
      final dogId = await ref.read(dogFormProvider.notifier).addDog(dog);
      success = dogId != null;
      
      // Upload photo if selected
      if (success && _selectedImage != null && dogId.isNotEmpty) {
        final photoUrl = await ref.read(dogFormProvider.notifier)
            .uploadPhoto(dogId, _selectedImage!);
        if (photoUrl != null && photoUrl.isNotEmpty) {
          // Update dog with photo URL
          await ref.read(dogFormProvider.notifier)
              .updateDog(dog.copyWith(photoUrl: photoUrl));
        }
      }
    }

    if (success && mounted) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_isEditing ? 'Dog updated successfully!' : 'Dog added successfully!'),
          backgroundColor: Colors.green,
        ),
      );
    } else if (mounted) {
      final error = ref.read(dogFormProvider).error;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to save dog: $error'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
