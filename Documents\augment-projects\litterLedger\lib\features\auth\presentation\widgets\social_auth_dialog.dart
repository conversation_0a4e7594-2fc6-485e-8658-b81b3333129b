import 'package:flutter/material.dart';

class SocialAuthDialog extends StatefulWidget {
  final String provider;
  final Function(String email, String displayName) onConfirm;

  const SocialAuthDialog({
    Key? key,
    required this.provider,
    required this.onConfirm,
  }) : super(key: key);

  @override
  State<SocialAuthDialog> createState() => _SocialAuthDialogState();
}

class _SocialAuthDialogState extends State<SocialAuthDialog> {
  final _emailController = TextEditingController();
  final _nameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // Pre-fill with demo data
    if (widget.provider == 'google') {
      _emailController.text = '<EMAIL>';
      _nameController.text = 'Your Name';
    } else if (widget.provider == 'apple') {
      _emailController.text = '<EMAIL>';
      _nameController.text = 'Your Name';
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            widget.provider == 'google' ? Icons.login : Icons.apple,
            color: widget.provider == 'google' ? Colors.red : Colors.black,
          ),
          const SizedBox(width: 8),
          Text('${widget.provider.toUpperCase()} Sign-In Demo'),
        ],
      ),
      content: SizedBox(
        width: 300,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Enter your ${widget.provider} account information for demo purposes:',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _emailController,
                keyboardType: TextInputType.emailAddress,
                decoration: InputDecoration(
                  labelText: 'Email',
                  prefixIcon: const Icon(Icons.email),
                  border: const OutlineInputBorder(),
                  hintText: widget.provider == 'google' 
                      ? '<EMAIL>' 
                      : '<EMAIL>',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your email';
                  }
                  if (!value.contains('@')) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Display Name',
                  prefixIcon: Icon(Icons.person),
                  border: OutlineInputBorder(),
                  hintText: 'Your Full Name',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue.shade600, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'This is demo mode. In production, this would use real ${widget.provider} OAuth.',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              widget.onConfirm(
                _emailController.text.trim(),
                _nameController.text.trim(),
              );
              Navigator.of(context).pop();
            }
          },
          child: Text('Sign In with ${widget.provider.toUpperCase()}'),
        ),
      ],
    );
  }
}
