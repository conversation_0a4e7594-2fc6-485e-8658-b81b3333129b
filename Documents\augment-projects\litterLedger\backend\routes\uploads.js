const express = require('express');
const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads', req.user.userId);
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const fileFilter = (req, file, cb) => {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, and WebP are allowed.'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 5 // Maximum 5 files per request
  }
});

// Upload dog photos
router.post('/dog-photos', [
  authenticateToken,
  upload.array('photos', 5)
], async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        error: 'No files uploaded'
      });
    }

    const processedFiles = [];

    for (const file of req.files) {
      try {
        // Create different sizes
        const sizes = [
          { name: 'thumbnail', width: 150, height: 150 },
          { name: 'medium', width: 500, height: 500 },
          { name: 'large', width: 1200, height: 1200 }
        ];

        const fileInfo = {
          original_name: file.originalname,
          filename: file.filename,
          size: file.size,
          mimetype: file.mimetype,
          sizes: {}
        };

        for (const size of sizes) {
          const outputPath = path.join(
            path.dirname(file.path),
            `${path.parse(file.filename).name}_${size.name}.webp`
          );

          await sharp(file.path)
            .resize(size.width, size.height, {
              fit: 'cover',
              position: 'center'
            })
            .webp({ quality: 85 })
            .toFile(outputPath);

          fileInfo.sizes[size.name] = {
            filename: path.basename(outputPath),
            width: size.width,
            height: size.height,
            url: `/uploads/${req.user.userId}/${path.basename(outputPath)}`
          };
        }

        // Create original URL
        fileInfo.url = `/uploads/${req.user.userId}/${file.filename}`;

        processedFiles.push(fileInfo);

      } catch (error) {
        console.error('Error processing file:', file.filename, error);
        // Continue with other files
      }
    }

    res.json({
      message: 'Files uploaded successfully',
      files: processedFiles
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({
      error: 'Failed to upload files'
    });
  }
});

// Upload profile photo
router.post('/profile-photo', [
  authenticateToken,
  upload.single('photo')
], async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: 'No file uploaded'
      });
    }

    const file = req.file;

    // Create different sizes for profile photo
    const sizes = [
      { name: 'thumbnail', width: 100, height: 100 },
      { name: 'medium', width: 300, height: 300 }
    ];

    const fileInfo = {
      original_name: file.originalname,
      filename: file.filename,
      size: file.size,
      mimetype: file.mimetype,
      sizes: {}
    };

    for (const size of sizes) {
      const outputPath = path.join(
        path.dirname(file.path),
        `${path.parse(file.filename).name}_${size.name}.webp`
      );

      await sharp(file.path)
        .resize(size.width, size.height, {
          fit: 'cover',
          position: 'center'
        })
        .webp({ quality: 90 })
        .toFile(outputPath);

      fileInfo.sizes[size.name] = {
        filename: path.basename(outputPath),
        width: size.width,
        height: size.height,
        url: `/uploads/${req.user.userId}/${path.basename(outputPath)}`
      };
    }

    fileInfo.url = `/uploads/${req.user.userId}/${file.filename}`;

    res.json({
      message: 'Profile photo uploaded successfully',
      file: fileInfo
    });

  } catch (error) {
    console.error('Profile photo upload error:', error);
    res.status(500).json({
      error: 'Failed to upload profile photo'
    });
  }
});

// Upload health record attachments
router.post('/health-records', [
  authenticateToken,
  upload.array('attachments', 3)
], async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        error: 'No files uploaded'
      });
    }

    const processedFiles = [];

    for (const file of req.files) {
      const fileInfo = {
        original_name: file.originalname,
        filename: file.filename,
        size: file.size,
        mimetype: file.mimetype,
        url: `/uploads/${req.user.userId}/${file.filename}`
      };

      // If it's an image, create a thumbnail
      if (file.mimetype.startsWith('image/')) {
        const thumbnailPath = path.join(
          path.dirname(file.path),
          `${path.parse(file.filename).name}_thumb.webp`
        );

        try {
          await sharp(file.path)
            .resize(200, 200, {
              fit: 'cover',
              position: 'center'
            })
            .webp({ quality: 80 })
            .toFile(thumbnailPath);

          fileInfo.thumbnail_url = `/uploads/${req.user.userId}/${path.basename(thumbnailPath)}`;
        } catch (error) {
          console.error('Error creating thumbnail:', error);
        }
      }

      processedFiles.push(fileInfo);
    }

    res.json({
      message: 'Health record attachments uploaded successfully',
      files: processedFiles
    });

  } catch (error) {
    console.error('Health record upload error:', error);
    res.status(500).json({
      error: 'Failed to upload health record attachments'
    });
  }
});

// Delete uploaded file
router.delete('/:filename', authenticateToken, async (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(__dirname, '../uploads', req.user.userId, filename);

    // Security check - ensure file belongs to user
    const userDir = path.join(__dirname, '../uploads', req.user.userId);
    const resolvedPath = path.resolve(filePath);
    const resolvedUserDir = path.resolve(userDir);

    if (!resolvedPath.startsWith(resolvedUserDir)) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    try {
      await fs.access(filePath);
      await fs.unlink(filePath);

      // Also try to delete associated thumbnails/sizes
      const baseName = path.parse(filename).name;
      const dir = path.dirname(filePath);
      
      try {
        const files = await fs.readdir(dir);
        const relatedFiles = files.filter(file => file.startsWith(baseName + '_'));
        
        for (const relatedFile of relatedFiles) {
          try {
            await fs.unlink(path.join(dir, relatedFile));
          } catch (error) {
            // Ignore errors for related files
          }
        }
      } catch (error) {
        // Ignore directory read errors
      }

      res.json({
        message: 'File deleted successfully'
      });

    } catch (error) {
      if (error.code === 'ENOENT') {
        return res.status(404).json({
          error: 'File not found'
        });
      }
      throw error;
    }

  } catch (error) {
    console.error('Delete file error:', error);
    res.status(500).json({
      error: 'Failed to delete file'
    });
  }
});

// Get user's uploaded files
router.get('/my-files', authenticateToken, async (req, res) => {
  try {
    const userDir = path.join(__dirname, '../uploads', req.user.userId);
    
    try {
      const files = await fs.readdir(userDir);
      const fileInfos = [];

      for (const filename of files) {
        try {
          const filePath = path.join(userDir, filename);
          const stats = await fs.stat(filePath);
          
          if (stats.isFile()) {
            fileInfos.push({
              filename,
              size: stats.size,
              created_at: stats.birthtime,
              url: `/uploads/${req.user.userId}/${filename}`
            });
          }
        } catch (error) {
          // Skip files that can't be read
        }
      }

      res.json({
        files: fileInfos.sort((a, b) => b.created_at - a.created_at)
      });

    } catch (error) {
      if (error.code === 'ENOENT') {
        return res.json({ files: [] });
      }
      throw error;
    }

  } catch (error) {
    console.error('Get files error:', error);
    res.status(500).json({
      error: 'Failed to get files'
    });
  }
});

module.exports = router;
