import 'package:flutter/material.dart';
import '../../domain/models/weight_record.dart';
import '../../domain/models/puppy.dart';
import '../screens/add_weight_record_screen.dart';

class WeightRecordsList extends StatelessWidget {
  final List<WeightRecord> records;
  final Puppy puppy;
  final VoidCallback? onRecordAdded;

  const WeightRecordsList({
    super.key,
    required this.records,
    required this.puppy,
    this.onRecordAdded,
  });

  @override
  Widget build(BuildContext context) {
    if (records.isEmpty) {
      return _buildEmptyState(context);
    }

    // Sort records by date (newest first)
    final sortedRecords = List<WeightRecord>.from(records)
      ..sort((a, b) => b.date.compareTo(a.date));

    return Column(
      children: [
        _buildSummaryCard(context, sortedRecords),
        const SizedBox(height: 16),
        Expanded(
          child: ListView.builder(
            itemCount: sortedRecords.length,
            itemBuilder: (context, index) {
              final record = sortedRecords[index];
              final previousRecord = index < sortedRecords.length - 1 
                  ? sortedRecords[index + 1] 
                  : null;
              
              return _buildWeightRecordCard(
                context, 
                record, 
                previousRecord,
                index == 0, // isLatest
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.monitor_weight,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No weight records yet',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start tracking ${puppy.displayName}\'s growth',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _navigateToAddWeight(context),
            icon: const Icon(Icons.add),
            label: const Text('Add First Weight'),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(BuildContext context, List<WeightRecord> sortedRecords) {
    final latestRecord = sortedRecords.first;
    final oldestRecord = sortedRecords.last;
    final weightGain = latestRecord.weight - oldestRecord.weight;
    final daysBetween = latestRecord.date.difference(oldestRecord.date).inDays;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Weight Summary',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Current Weight',
                    latestRecord.weightString,
                    Icons.monitor_weight,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Total Gain',
                    weightGain > 0 
                        ? '+${weightGain.toStringAsFixed(2)} kg'
                        : '${weightGain.toStringAsFixed(2)} kg',
                    Icons.trending_up,
                    weightGain >= 0 ? Colors.green : Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Records',
                    '${sortedRecords.length}',
                    Icons.assignment,
                    Colors.purple,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Days Tracked',
                    '$daysBetween',
                    Icons.calendar_today,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 10,
            color: Colors.grey,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildWeightRecordCard(
    BuildContext context,
    WeightRecord record,
    WeightRecord? previousRecord,
    bool isLatest,
  ) {
    double? weightChange;
    if (previousRecord != null) {
      weightChange = record.weight - previousRecord.weight;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => _navigateToEditWeight(context, record),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isLatest 
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.monitor_weight,
                  color: isLatest ? Colors.green : Colors.blue,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          record.weightString,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                        if (weightChange != null) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: weightChange >= 0 
                                  ? Colors.green.withValues(alpha: 0.1)
                                  : Colors.red.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              '${weightChange >= 0 ? '+' : ''}${weightChange.toStringAsFixed(2)} kg',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                                color: weightChange >= 0 ? Colors.green : Colors.red,
                              ),
                            ),
                          ),
                        ],
                        if (isLatest) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.green.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              'LATEST',
                              style: TextStyle(
                                fontSize: 9,
                                fontWeight: FontWeight.w600,
                                color: Colors.green,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 14,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _formatDate(record.date),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[600],
                              ),
                        ),
                        if (record.ageInDays != null) ...[
                          const SizedBox(width: 12),
                          Icon(
                            Icons.cake,
                            size: 14,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            record.ageString,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Colors.grey[600],
                                ),
                          ),
                        ],
                      ],
                    ),
                    if (record.notes != null && record.notes!.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        record.notes!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[700],
                              fontStyle: FontStyle.italic,
                            ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _navigateToAddWeight(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddWeightRecordScreen(puppy: puppy),
      ),
    ).then((_) => onRecordAdded?.call());
  }

  void _navigateToEditWeight(BuildContext context, WeightRecord record) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddWeightRecordScreen(
          puppy: puppy,
          weightRecord: record,
        ),
      ),
    ).then((_) => onRecordAdded?.call());
  }
}
