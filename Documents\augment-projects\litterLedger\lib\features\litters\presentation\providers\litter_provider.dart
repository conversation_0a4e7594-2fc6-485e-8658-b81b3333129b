import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/models/litter.dart';
import '../../domain/models/puppy.dart';
import '../../domain/models/weight_record.dart';
import '../../data/repositories/litter_repository.dart';

// Repository provider
final litterRepositoryProvider = Provider<LitterRepository>((ref) {
  return LitterRepository();
});

// Litters provider - gets all user litters
final littersProvider = FutureProvider<List<Litter>>((ref) async {
  final repository = ref.read(litterRepositoryProvider);
  return repository.getUserLitters();
});

// Single litter provider
final singleLitterProvider = FutureProvider.family<Litter?, String>((ref, litterId) async {
  final repository = ref.read(litterRepositoryProvider);
  return repository.getLitterById(litterId);
});

// Litter puppies provider
final litterPuppiesProvider = FutureProvider.family<List<Puppy>, String>((ref, litterId) async {
  final repository = ref.read(litterRepositoryProvider);
  return repository.getLitterPuppies(litterId);
});

// All user puppies provider
final allPuppiesProvider = FutureProvider<List<Puppy>>((ref) async {
  final repository = ref.read(litterRepositoryProvider);
  return repository.getUserPuppies();
});

// Puppy weight records provider
final puppyWeightRecordsProvider = FutureProvider.family<List<WeightRecord>, String>((ref, puppyId) async {
  final repository = ref.read(litterRepositoryProvider);
  return repository.getPuppyWeightRecords(puppyId);
});

// All weight records provider
final allWeightRecordsProvider = FutureProvider<List<WeightRecord>>((ref) async {
  final repository = ref.read(litterRepositoryProvider);
  return repository.getUserWeightRecords();
});

// Litter management state
class LitterManagementState {
  final bool isLoading;
  final String? error;
  final String? successMessage;

  const LitterManagementState({
    this.isLoading = false,
    this.error,
    this.successMessage,
  });

  LitterManagementState copyWith({
    bool? isLoading,
    String? error,
    String? successMessage,
  }) {
    return LitterManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      successMessage: successMessage,
    );
  }
}

// Litter management notifier
class LitterManagementNotifier extends StateNotifier<LitterManagementState> {
  final LitterRepository _repository;

  LitterManagementNotifier(this._repository) : super(const LitterManagementState());

  // Create a new litter
  Future<String?> createLitter(Litter litter) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final createdLitter = await _repository.createLitter(litter);
      state = state.copyWith(
        isLoading: false,
        successMessage: 'Litter "${createdLitter.name}" created successfully!',
      );
      return createdLitter.id;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return null;
    }
  }

  // Update an existing litter
  Future<bool> updateLitter(Litter litter) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.updateLitter(litter);
      state = state.copyWith(
        isLoading: false,
        successMessage: 'Litter "${litter.name}" updated successfully!',
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  // Delete a litter
  Future<bool> deleteLitter(String litterId, String litterName) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.deleteLitter(litterId);
      state = state.copyWith(
        isLoading: false,
        successMessage: 'Litter "$litterName" deleted successfully!',
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  // Clear messages
  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }
}

// Litter management provider
final litterManagementProvider = StateNotifierProvider<LitterManagementNotifier, LitterManagementState>((ref) {
  final repository = ref.read(litterRepositoryProvider);
  return LitterManagementNotifier(repository);
});

// Puppy management notifier
class PuppyManagementNotifier extends StateNotifier<LitterManagementState> {
  final LitterRepository _repository;

  PuppyManagementNotifier(this._repository) : super(const LitterManagementState());

  // Create a new puppy
  Future<String?> createPuppy(Puppy puppy) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final createdPuppy = await _repository.createPuppy(puppy);
      state = state.copyWith(
        isLoading: false,
        successMessage: 'Puppy "${createdPuppy.displayName}" added successfully!',
      );
      return createdPuppy.id;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return null;
    }
  }

  // Update an existing puppy
  Future<bool> updatePuppy(Puppy puppy) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.updatePuppy(puppy);
      state = state.copyWith(
        isLoading: false,
        successMessage: 'Puppy "${puppy.displayName}" updated successfully!',
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  // Delete a puppy
  Future<bool> deletePuppy(String puppyId, String puppyName) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.deletePuppy(puppyId);
      state = state.copyWith(
        isLoading: false,
        successMessage: 'Puppy "$puppyName" removed successfully!',
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  // Clear messages
  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }
}

// Puppy management provider
final puppyManagementProvider = StateNotifierProvider<PuppyManagementNotifier, LitterManagementState>((ref) {
  final repository = ref.read(litterRepositoryProvider);
  return PuppyManagementNotifier(repository);
});

// Weight record management notifier
class WeightRecordManagementNotifier extends StateNotifier<LitterManagementState> {
  final LitterRepository _repository;

  WeightRecordManagementNotifier(this._repository) : super(const LitterManagementState());

  // Create a new weight record
  Future<String?> createWeightRecord(WeightRecord record) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final createdRecord = await _repository.createWeightRecord(record);
      state = state.copyWith(
        isLoading: false,
        successMessage: 'Weight record (${createdRecord.weightDisplay}) added successfully!',
      );
      return createdRecord.id;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return null;
    }
  }

  // Update an existing weight record
  Future<bool> updateWeightRecord(WeightRecord record) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.updateWeightRecord(record);
      state = state.copyWith(
        isLoading: false,
        successMessage: 'Weight record updated successfully!',
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  // Delete a weight record
  Future<bool> deleteWeightRecord(String recordId) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _repository.deleteWeightRecord(recordId);
      state = state.copyWith(
        isLoading: false,
        successMessage: 'Weight record deleted successfully!',
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  // Clear messages
  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }
}

// Weight record management provider
final weightRecordManagementProvider = StateNotifierProvider<WeightRecordManagementNotifier, LitterManagementState>((ref) {
  final repository = ref.read(litterRepositoryProvider);
  return WeightRecordManagementNotifier(repository);
});
